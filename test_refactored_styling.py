"""
Test script for the refactored Excel styling system

This script validates:
1. Backward compatibility with existing styling system
2. New reference-based styling system functionality
3. Configuration-based styling
4. Style registry operations
5. Integration with report generation
"""

import sys
import os
import pandas as pd
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_excel_styling_module():
    """Test the excel_styling module functionality"""
    print("Testing excel_styling module...")
    
    from excel_styling import (
        ExcelCellStyle, FontStyle, FillStyle, BorderStyle, CellBorders, AlignmentStyle,
        FontWeight, HorizontalAlignment, VerticalAlignment, BorderStyleType, FillType,
        StylingPresets, StylingUtils, ColumnStyleFactory, StyleRegistry, StyleConfiguration,
        get_style_registry, get_style_configuration, initialize_styling_system
    )
    
    # Test basic style creation
    font_style = FontStyle(color="FF000000", weight=FontWeight.BOLD)
    fill_style = FillStyle(fill_type=FillType.SOLID, start_color="FFFF0000")
    alignment_style = AlignmentStyle(horizontal=HorizontalAlignment.CENTER)
    borders = CellBorders.all_borders()
    
    cell_style = ExcelCellStyle(
        font=font_style,
        fill=fill_style,
        alignment=alignment_style,
        borders=borders,
        number_format="0.00"
    )
    
    print("✓ Basic style creation works")
    
    # Test styling presets
    header_style = StylingPresets.get_header_style()
    text_style = StylingPresets.get_text_data_style()
    percentage_style = StylingPresets.get_percentage_data_style(2)
    
    print("✓ Styling presets work")
    
    # Test style registry
    registry = get_style_registry()
    assert registry.has_style("default_header")
    assert registry.has_style("text_data")
    assert registry.has_style("percentage_data")
    
    print("✓ Style registry works")
    
    # Test column style factory
    text_column = ColumnStyleFactory.create_text_column("Test Column", width=20)
    percentage_column = ColumnStyleFactory.create_percentage_column("Test %", width=15)
    
    print("✓ Column style factory works")
    
    return True

def test_backward_compatibility():
    """Test backward compatibility with existing styling system"""
    print("\nTesting backward compatibility...")
    
    from report_definitions import ColumnDefinition
    from excel_styling import ExcelCellStyle, FontStyle, FillStyle, FillType, FontWeight
    
    # Test old-style column definition
    old_column = ColumnDefinition(
        name='Test Column',
        data_type='string',
        width=20,
        alignment='center'
    )
    
    # Should automatically get default styling
    assert old_column.header_style is not None
    assert old_column.data_style is not None
    assert old_column.data_style.alignment.horizontal.value == 'center'
    
    print("✓ Old-style column definition works")
    
    # Test direct styling (backward compatibility)
    styled_column = ColumnDefinition(
        name='Styled Column',
        data_type='float',
        header_style=ExcelCellStyle(
            font=FontStyle(color="FFFFFFFF", weight=FontWeight.BOLD),
            fill=FillStyle(fill_type=FillType.SOLID, start_color="FF0066CC")
        )
    )
    
    assert styled_column.header_style.font.color == "FFFFFFFF"
    assert styled_column.header_style.fill.start_color == "FF0066CC"
    
    print("✓ Direct styling (backward compatibility) works")
    
    return True

def test_reference_based_styling():
    """Test new reference-based styling system"""
    print("\nTesting reference-based styling...")
    
    from report_definitions import ColumnDefinition
    from excel_styling import get_style_registry
    
    # Test reference-based styling
    ref_column = ColumnDefinition(
        name='Reference Column',
        data_type='float',
        header_style_name='blue_header',  # This should be resolved from config
        data_style_name='percentage_data'
    )
    
    # The styles should be resolved in __post_init__
    assert ref_column.header_style is not None
    assert ref_column.data_style is not None
    
    print("✓ Reference-based styling works")
    
    return True

def test_configuration_based_styling():
    """Test configuration-based styling system"""
    print("\nTesting configuration-based styling...")
    
    from excel_styling import StyleConfiguration, initialize_styling_system
    
    # Initialize with configuration file
    config_file = "styling_config.yaml"
    if Path(config_file).exists():
        initialize_styling_system(config_file)
        config = StyleConfiguration(config_file)
        
        # Test report-specific styling
        telephone_header_style = config.get_report_style(
            "Customer Service Performance (Telephone)", 
            "header"
        )
        
        telephone_percentage_style = config.get_report_style(
            "Customer Service Performance (Telephone)", 
            "data", 
            "ABANDON %"
        )
        
        assert telephone_header_style is not None
        assert telephone_percentage_style is not None
        
        print("✓ Configuration-based styling works")
    else:
        print("⚠ Configuration file not found, skipping configuration test")
    
    return True

def test_report_generation_integration():
    """Test integration with report generation"""
    print("\nTesting report generation integration...")
    
    from report_definitions import ReportDefinition, ColumnDefinition
    from excel_styling import ColumnStyleFactory
    
    # Create a test report with mixed styling approaches
    test_columns = [
        # Old-style column
        ColumnDefinition('Old Style', 'string', width=15, alignment='left'),
        
        # Direct styling
        ColumnDefinition('Direct Style', 'integer', width=12),
        
        # Reference-based styling
        ColumnDefinition('Reference Style', 'float', width=15, 
                        header_style_name='default_header', 
                        data_style_name='percentage_data'),
        
        # Factory-created column
        ColumnStyleFactory.create_percentage_column('Factory %', width=12)
    ]
    
    test_report = ReportDefinition(
        name='Test Report',
        description='Test report for styling validation',
        database='test',
        sql_file='test.sql',
        columns=test_columns,
        parameter_count=0,
        parameter_order=[],
        special_processing=[]
    )
    
    # Test that all columns have proper styling
    for col in test_report.columns:
        header_style = test_report.get_column_header_style(col.name)
        data_style = test_report.get_column_data_style(col.name)
        
        assert header_style is not None
        assert data_style is not None
    
    print("✓ Report generation integration works")
    
    return True

def test_excel_output():
    """Test actual Excel file generation with new styling system"""
    print("\nTesting Excel output generation...")
    
    try:
        import openpyxl
        from excel_styling import StylingUtils
        from report_definitions import ReportDefinition
        from excel_styling import ColumnStyleFactory
        
        # Create test data
        test_data = {
            'Name': ['Alice', 'Bob', 'Charlie'],
            'Age': [25, 30, 35],
            'Salary': [50000.00, 60000.00, 70000.00],
            'Performance %': [0.85, 0.92, 0.78]
        }
        df = pd.DataFrame(test_data)
        
        # Create styled columns
        columns = [
            ColumnStyleFactory.create_text_column('Name', width=20),
            ColumnStyleFactory.create_number_column('Age', width=10),
            ColumnStyleFactory.create_number_column('Salary', width=15, decimal_places=2),
            ColumnStyleFactory.create_percentage_column('Performance %', width=15)
        ]
        
        # Create test report
        test_report = ReportDefinition(
            name='Excel Test Report',
            description='Test report for Excel output validation',
            database='test',
            sql_file='test.sql',
            columns=columns,
            parameter_count=0,
            parameter_order=[],
            special_processing=[]
        )
        
        # Create Excel file
        output_file = 'test_refactored_styling_output.xlsx'
        workbook = openpyxl.Workbook()
        worksheet = workbook.active
        worksheet.title = 'Test Sheet'
        
        # Write data
        for col_idx, col_name in enumerate(df.columns, 1):
            worksheet.cell(row=1, column=col_idx, value=col_name)
            for row_idx, value in enumerate(df[col_name], 2):
                worksheet.cell(row=row_idx, column=col_idx, value=value)
        
        # Apply styling
        for col_idx, col_name in enumerate(df.columns, 1):
            # Header styling
            header_cell = worksheet.cell(row=1, column=col_idx)
            header_style = test_report.get_column_header_style(col_name)
            StylingUtils.apply_openpyxl_style(header_cell, header_style)
            
            # Data styling
            for row_idx in range(2, len(df) + 2):
                data_cell = worksheet.cell(row=row_idx, column=col_idx)
                data_style = test_report.get_column_data_style(col_name)
                StylingUtils.apply_openpyxl_style(data_cell, data_style)
            
            # Set column width
            col_def = next((col for col in columns if col.name == col_name), None)
            if col_def and col_def.width:
                col_letter = chr(64 + col_idx)
                worksheet.column_dimensions[col_letter].width = col_def.width
        
        # Save file
        workbook.save(output_file)
        
        print(f"✓ Excel file generated successfully: {output_file}")
        
        # Validate the file
        if Path(output_file).exists():
            wb = openpyxl.load_workbook(output_file)
            ws = wb.active
            
            # Check header formatting
            header_cell = ws['A1']
            if header_cell.font.bold:
                print("✓ Header formatting applied correctly")
            else:
                print("⚠ Header formatting may not be applied correctly")
            
            # Check percentage formatting
            pct_cell = ws['D2']  # Performance % column
            if pct_cell.number_format in ['0.00', '0.00%']:
                print("✓ Percentage formatting applied correctly")
            else:
                print(f"⚠ Percentage formatting: {pct_cell.number_format}")
        
        return True
        
    except ImportError:
        print("⚠ openpyxl not available, skipping Excel output test")
        return True
    except Exception as e:
        print(f"✗ Excel output test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("REFACTORED EXCEL STYLING SYSTEM - VALIDATION TESTS")
    print("=" * 60)
    
    tests = [
        test_excel_styling_module,
        test_backward_compatibility,
        test_reference_based_styling,
        test_configuration_based_styling,
        test_report_generation_integration,
        test_excel_output
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"✗ {test.__name__} failed with error: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print(f"TEST RESULTS: {passed} passed, {failed} failed")
    print("=" * 60)
    
    if failed == 0:
        print("🎉 All tests passed! The refactored styling system is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
