{"Customer Service Performance (Multiple Channel)": {"filename": "Customer Service Performance (Multiple Channel)_JAN2025.xlsx", "report_name": "Customer Service Performance (Multiple Channel)", "shape": [3819, 6], "columns": ["Case Number", "Channel", "Actual Start DateTime", "Completed DateTime", "Available SLA (Second)", "Actual SLA (Second)"], "column_count": 6, "row_count": 3819, "data_types": {"Case Number": "int64", "Channel": "object", "Actual Start DateTime": "datetime64[ns]", "Completed DateTime": "datetime64[ns]", "Available SLA (Second)": "int64", "Actual SLA (Second)": "int64"}, "sample_data": {"Case Number": {"first_value": "5173207", "last_value": "5186062", "unique_count": 3819, "data_type": "int64"}, "Channel": {"first_value": "Email", "last_value": "Open Portal", "unique_count": 2, "data_type": "object"}, "Actual Start DateTime": {"first_value": "2025-01-01 10:01:00", "last_value": "2025-01-31 09:04:00", "unique_count": 3322, "data_type": "datetime64[ns]"}, "Completed DateTime": {"first_value": "2025-01-01 10:10:00", "last_value": "2025-01-31 09:08:00", "unique_count": 3314, "data_type": "datetime64[ns]"}, "Available SLA (Second)": {"first_value": "900", "last_value": "900", "unique_count": 1, "data_type": "int64"}, "Actual SLA (Second)": {"first_value": "540", "last_value": "240", "unique_count": 148, "data_type": "int64"}}, "special_rows": []}, "Customer Service Performance (Telephone)": {"filename": "Customer Service Performance (Telephone)_JAN2025.xlsx", "report_name": "Customer Service Performance (Telephone)", "shape": [32, 9], "columns": ["DATE CALL", "CALL OFFER", "CALL HANDLE", "CALL WITHIN 10", "ABANDON SHORT", "ABANDON LONG", "ABANDON %", "ANSWER %", "SLA %"], "column_count": 9, "row_count": 32, "data_types": {"DATE CALL": "object", "CALL OFFER": "int64", "CALL HANDLE": "int64", "CALL WITHIN 10": "int64", "ABANDON SHORT": "int64", "ABANDON LONG": "int64", "ABANDON %": "float64", "ANSWER %": "float64", "SLA %": "float64"}, "sample_data": {"DATE CALL": {"first_value": "2025-01-01 00:00:00", "last_value": "MTD", "unique_count": 32, "data_type": "object"}, "CALL OFFER": {"first_value": "49", "last_value": "8313", "unique_count": 30, "data_type": "int64"}, "CALL HANDLE": {"first_value": "49", "last_value": "8071", "unique_count": 30, "data_type": "int64"}, "CALL WITHIN 10": {"first_value": "48", "last_value": "7193", "unique_count": 28, "data_type": "int64"}, "ABANDON SHORT": {"first_value": "0", "last_value": "15", "unique_count": 4, "data_type": "int64"}, "ABANDON LONG": {"first_value": "0", "last_value": "227", "unique_count": 16, "data_type": "int64"}, "ABANDON %": {"first_value": "0.0", "last_value": "2.911103091543366", "unique_count": 28, "data_type": "float64"}, "ANSWER %": {"first_value": "100.0", "last_value": "97.08889690845663", "unique_count": 28, "data_type": "float64"}, "SLA %": {"first_value": "97.96", "last_value": "86.70756646216769", "unique_count": 32, "data_type": "float64"}}, "special_rows": [{"row_index": 31, "first_column_value": "MTD", "row_data": {"DATE CALL": "MTD", "CALL OFFER": 8313, "CALL HANDLE": 8071, "CALL WITHIN 10": 7193, "ABANDON SHORT": 15, "ABANDON LONG": 227, "ABANDON %": 2.911103091543366, "ANSWER %": 97.08889690845663, "SLA %": 86.70756646216769}}]}, "Incident Management (Case Acknowledgement)": {"filename": "Incident Management (Case Acknowledgement)_JAN2025.xlsx", "report_name": "Incident Management (Case Acknowledgement)", "shape": [1144, 8], "columns": ["Case Number", "Case Name", "Task Number", "SLA Flag", "Actual Start DateTime", "Completed DateTime", "Available SLA (Second)", "Actual SLA (Second)"], "column_count": 8, "row_count": 1144, "data_types": {"Case Number": "int64", "Case Name": "object", "Task Number": "int64", "SLA Flag": "int64", "Actual Start DateTime": "datetime64[ns]", "Completed DateTime": "datetime64[ns]", "Available SLA (Second)": "int64", "Actual SLA (Second)": "int64"}, "sample_data": {"Case Number": {"first_value": "5173277", "last_value": "5185988", "unique_count": 1144, "data_type": "int64"}, "Case Name": {"first_value": "[Emailed] : No. MOF 357-0002419397 - PERMOHONAN PENGESAHAN PAPARAN SIJIL MAYA", "last_value": "[Emailed] : <PERSON>", "unique_count": 1084, "data_type": "object"}, "Task Number": {"first_value": "1767478", "last_value": "1774840", "unique_count": 1144, "data_type": "int64"}, "SLA Flag": {"first_value": "2", "last_value": "2", "unique_count": 1, "data_type": "int64"}, "Actual Start DateTime": {"first_value": "2025-01-01 22:27:00", "last_value": "2025-01-30 10:53:00", "unique_count": 1081, "data_type": "datetime64[ns]"}, "Completed DateTime": {"first_value": "2025-01-01 22:32:51", "last_value": "2025-01-30 11:03:51", "unique_count": 1142, "data_type": "datetime64[ns]"}, "Available SLA (Second)": {"first_value": "900", "last_value": "900", "unique_count": 1, "data_type": "int64"}, "Actual SLA (Second)": {"first_value": "351", "last_value": "651", "unique_count": 433, "data_type": "int64"}}, "special_rows": []}, "Incident Management (RIT)": {"filename": "Incident Management (RIT)_JAN2025.xlsx", "report_name": "Incident Management (RIT)", "shape": [1111, 8], "columns": ["Case Number", "Case Name", "Task Number", "SLA Flag", "Actual Start DateTime", "Completed DateTime", "Available SLA (Second)", "Actual SLA (Second)"], "column_count": 8, "row_count": 1111, "data_types": {"Case Number": "int64", "Case Name": "object", "Task Number": "int64", "SLA Flag": "int64", "Actual Start DateTime": "datetime64[ns]", "Completed DateTime": "datetime64[ns]", "Available SLA (Second)": "int64", "Actual SLA (Second)": "int64"}, "sample_data": {"Case Number": {"first_value": "5173256", "last_value": "5181799", "unique_count": 1111, "data_type": "int64"}, "Case Name": {"first_value": "Surat Setuju Terima LA240000000050666 telah diserah tetapi tidak diterima oleh Syarikat ", "last_value": "LPO tidak dapat diluluskan", "unique_count": 1053, "data_type": "object"}, "Task Number": {"first_value": "1767474", "last_value": "1774769", "unique_count": 1111, "data_type": "int64"}, "SLA Flag": {"first_value": "3", "last_value": "3", "unique_count": 1, "data_type": "int64"}, "Actual Start DateTime": {"first_value": "2025-01-01 20:28:30", "last_value": "2025-01-28 16:16:24", "unique_count": 1111, "data_type": "datetime64[ns]"}, "Completed DateTime": {"first_value": "2025-01-01 20:37:53", "last_value": "2025-01-28 16:22:47", "unique_count": 1110, "data_type": "datetime64[ns]"}, "Available SLA (Second)": {"first_value": "14400", "last_value": "14400", "unique_count": 1, "data_type": "int64"}, "Actual SLA (Second)": {"first_value": "563", "last_value": "383", "unique_count": 436, "data_type": "int64"}}, "special_rows": []}, "Incident Management S123": {"filename": "Incident Management S123_JAN2025.xlsx", "report_name": "Incident Management S123", "shape": [1103, 8], "columns": ["Case Number", "Case Name", "Task Number", "SLA Flag", "Actual Start DateTime", "Completed DateTime", "Available SLA (Day)", "Actual SLA (Day)"], "column_count": 8, "row_count": 1103, "data_types": {"Case Number": "int64", "Case Name": "object", "Task Number": "int64", "SLA Flag": "object", "Actual Start DateTime": "datetime64[ns]", "Completed DateTime": "datetime64[ns]", "Available SLA (Day)": "int64", "Actual SLA (Day)": "int64"}, "sample_data": {"Case Number": {"first_value": "5173256", "last_value": "5181799", "unique_count": 1103, "data_type": "int64"}, "Case Name": {"first_value": "Surat Setuju Terima LA240000000050666 telah diserah tetapi tidak diterima oleh Syarikat ", "last_value": "LPO tidak dapat diluluskan", "unique_count": 1045, "data_type": "object"}, "Task Number": {"first_value": "1767475", "last_value": "1774770", "unique_count": 1103, "data_type": "int64"}, "SLA Flag": {"first_value": "s1", "last_value": "s1", "unique_count": 3, "data_type": "object"}, "Actual Start DateTime": {"first_value": "2025-01-01 20:37:53", "last_value": "2025-01-28 16:22:47", "unique_count": 1102, "data_type": "datetime64[ns]"}, "Completed DateTime": {"first_value": "2025-01-01 20:46:53", "last_value": "2025-01-28 16:27:51", "unique_count": 1103, "data_type": "datetime64[ns]"}, "Available SLA (Day)": {"first_value": "1", "last_value": "1", "unique_count": 3, "data_type": "int64"}, "Actual SLA (Day)": {"first_value": "0", "last_value": "0", "unique_count": 6, "data_type": "int64"}}, "special_rows": []}, "Incident Management S4": {"filename": "Incident Management S4_JAN2025.xlsx", "report_name": "Incident Management S4", "shape": [8, 12], "columns": ["Case Number", "Redmine No.", "Case Name\t", "Task Name", "SLA Flag", "Actual Start DateTime", "Completed DateTime", "Duration", "Available SLA (Day)", "Actual SLA (Day)", "Implementation Issue?", "Exceed SLA (Day)"], "column_count": 12, "row_count": 8, "data_types": {"Case Number": "int64", "Redmine No.": "int64", "Case Name\t": "object", "Task Name": "object", "SLA Flag": "int64", "Actual Start DateTime": "datetime64[ns]", "Completed DateTime": "datetime64[ns]", "Duration": "object", "Available SLA (Day)": "int64", "Actual SLA (Day)": "int64", "Implementation Issue?": "object", "Exceed SLA (Day)": "int64"}, "sample_data": {"Case Number": {"first_value": "4510040", "last_value": "5176244", "unique_count": 8, "data_type": "int64"}, "Redmine No.": {"first_value": "32682", "last_value": "37759", "unique_count": 8, "data_type": "int64"}, "Case Name\t": {"first_value": "Permanent Case for Bug #32682", "last_value": "Permanent Case for Bug #37759", "unique_count": 8, "data_type": "object"}, "Task Name": {"first_value": "Assigned to Group IT Specialist(Production Support)", "last_value": "Assigned to Group IT Specialist(Production Support)", "unique_count": 1, "data_type": "object"}, "SLA Flag": {"first_value": "4", "last_value": "4", "unique_count": 1, "data_type": "int64"}, "Actual Start DateTime": {"first_value": "2024-12-04 14:06:13", "last_value": "2025-01-10 10:21:10", "unique_count": 8, "data_type": "datetime64[ns]"}, "Completed DateTime": {"first_value": "2025-01-27 10:30:00", "last_value": "2025-01-10 16:27:00", "unique_count": 8, "data_type": "datetime64[ns]"}, "Duration": {"first_value": "61 days 0 hours", "last_value": "28 days 0 hours", "unique_count": 4, "data_type": "object"}, "Available SLA (Day)": {"first_value": "61", "last_value": "28", "unique_count": 4, "data_type": "int64"}, "Actual SLA (Day)": {"first_value": "54", "last_value": "0", "unique_count": 8, "data_type": "int64"}, "Implementation Issue?": {"first_value": "YES", "last_value": "NO", "unique_count": 2, "data_type": "object"}, "Exceed SLA (Day)": {"first_value": "0", "last_value": "0", "unique_count": 1, "data_type": "int64"}}, "special_rows": []}, "Service Request": {"filename": "Service Request_JAN2025.xlsx", "report_name": "Service Request", "shape": [1, 12], "columns": ["TICKET NUMBER", "CATEGORY", "CREATED DATE", "REQUEST SUBJECT", "REASON FOR REQUEST", "EXPECTED START", "EXPECTED COMPLETED", "EXPECTED DURATION", "ACTUAL START", "ACTUAL COMPLETED", "ACTUAL DURATION", "EXCEED DURATION"], "column_count": 12, "row_count": 1, "data_types": {"TICKET NUMBER": "object", "CATEGORY": "object", "CREATED DATE": "datetime64[ns]", "REQUEST SUBJECT": "object", "REASON FOR REQUEST": "object", "EXPECTED START": "datetime64[ns]", "EXPECTED COMPLETED": "datetime64[ns]", "EXPECTED DURATION": "int64", "ACTUAL START": "datetime64[ns]", "ACTUAL COMPLETED": "datetime64[ns]", "ACTUAL DURATION": "int64", "EXCEED DURATION": "int64"}, "sample_data": {"TICKET NUMBER": {"first_value": "#0000007614", "last_value": "#0000007614", "unique_count": 1, "data_type": "object"}, "CATEGORY": {"first_value": "Production Shutdown", "last_value": "Production Shutdown", "unique_count": 1, "data_type": "object"}, "CREATED DATE": {"first_value": "2023-12-13 14:47:24", "last_value": "2023-12-13 14:47:24", "unique_count": 1, "data_type": "datetime64[ns]"}, "REQUEST SUBJECT": {"first_value": "Production eP System Shutdown for YEP  8 Jan 2024", "last_value": "Production eP System Shutdown for YEP  8 Jan 2024", "unique_count": 1, "data_type": "object"}, "REASON FOR REQUEST": {"first_value": "To execute YEP process", "last_value": "To execute YEP process", "unique_count": 1, "data_type": "object"}, "EXPECTED START": {"first_value": "2024-01-08 18:01:00", "last_value": "2024-01-08 18:01:00", "unique_count": 1, "data_type": "datetime64[ns]"}, "EXPECTED COMPLETED": {"first_value": "2024-01-09 00:01:00", "last_value": "2024-01-09 00:01:00", "unique_count": 1, "data_type": "datetime64[ns]"}, "EXPECTED DURATION": {"first_value": "6", "last_value": "6", "unique_count": 1, "data_type": "int64"}, "ACTUAL START": {"first_value": "2024-01-08 18:01:00", "last_value": "2024-01-08 18:01:00", "unique_count": 1, "data_type": "datetime64[ns]"}, "ACTUAL COMPLETED": {"first_value": "2024-01-08 23:52:17", "last_value": "2024-01-08 23:52:17", "unique_count": 1, "data_type": "datetime64[ns]"}, "ACTUAL DURATION": {"first_value": "5", "last_value": "5", "unique_count": 1, "data_type": "int64"}, "EXCEED DURATION": {"first_value": "0", "last_value": "0", "unique_count": 1, "data_type": "int64"}}, "special_rows": []}, "System Availability (Infra)": {"filename": "System Availability (Infra)_JAN2025.xlsx", "report_name": "System Availability (Infra)", "shape": [174, 4], "columns": ["HOST GROUP", "DOWNTIME", "COUNTER", "ALL DOWN FLAG"], "column_count": 4, "row_count": 174, "data_types": {"HOST GROUP": "object", "DOWNTIME": "object", "COUNTER": "int64", "ALL DOWN FLAG": "object"}, "sample_data": {"HOST GROUP": {"first_value": "portal", "last_value": "bpm", "unique_count": 3, "data_type": "object"}, "DOWNTIME": {"first_value": "2025-01-01 22:50", "last_value": "2025-01-31 15:30", "unique_count": 170, "data_type": "object"}, "COUNTER": {"first_value": "2", "last_value": "1", "unique_count": 11, "data_type": "int64"}, "ALL DOWN FLAG": {"first_value": "NO", "last_value": "NO", "unique_count": 2, "data_type": "object"}}, "special_rows": []}, "System Performance (Response Time)": {"filename": "System Performance (Response Time)_JAN2025.xlsx", "report_name": "System Performance (Response Time)", "shape": [18, 5], "columns": ["System Performance", "Januari 2025", "Unnamed: 2", "Unnamed: 3", "Unnamed: 4"], "column_count": 5, "row_count": 18, "data_types": {"System Performance": "object", "Januari 2025": "object", "Unnamed: 2": "object", "Unnamed: 3": "object", "Unnamed: 4": "object"}, "sample_data": {"System Performance": {"first_value": "<PERSON><PERSON><PERSON> (16)", "last_value": "Non Compliance %", "unique_count": 18, "data_type": "object"}, "Januari 2025": {"first_value": "Exceed", "last_value": "28", "unique_count": 11, "data_type": "object"}, "Unnamed: 2": {"first_value": "Within", "last_value": "14026", "unique_count": 17, "data_type": "object"}, "Unnamed: 3": {"first_value": "Total", "last_value": "14054", "unique_count": 17, "data_type": "object"}, "Unnamed: 4": {"first_value": "Exceed %", "last_value": "1.06598125", "unique_count": 12, "data_type": "object"}}, "special_rows": [{"row_index": 0, "first_column_value": "<PERSON><PERSON><PERSON> (16)", "row_data": {"System Performance": "<PERSON><PERSON><PERSON> (16)", "Januari 2025": "Exceed", "Unnamed: 2": "Within", "Unnamed: 3": "Total", "Unnamed: 4": "Exceed %"}}, {"row_index": 17, "first_column_value": "Non Compliance %", "row_data": {"System Performance": "Non Compliance %", "Januari 2025": NaN, "Unnamed: 2": NaN, "Unnamed: 3": NaN, "Unnamed: 4": 1.06598125}}]}}