# Enhanced Excel Styling Guide

This guide explains how to use the enhanced ColumnDefinition class with Excel styling management in the POMS Report Generator.

## Overview

The enhanced ColumnDefinition class now supports comprehensive Excel styling including:
- Font styling (name, size, color, weight, italic, underline)
- Fill/background colors and patterns
- Borders (style, color, individual sides)
- Alignment (horizontal, vertical, text wrapping)
- Number formatting
- Predefined styling presets for different data types

## Key Features

### 1. Individual Column Styling
Each column can have separate styling for headers and data cells:

```python
from report_definitions import ColumnDefinition, ExcelCellStyle, FontStyle, FillStyle

# Create a column with custom styling
column = ColumnDefinition(
    name='Sales Amount',
    data_type='float',
    width=15,
    header_style=ExcelCellStyle(
        font=FontStyle(color="FFFFFFFF", weight=FontWeight.BOLD),
        fill=FillStyle(fill_type=FillType.SOLID, start_color="FF0066CC")
    ),
    data_style=ExcelCellStyle(
        alignment=AlignmentStyle(horizontal=HorizontalAlignment.RIGHT),
        number_format="0.00"
    )
)
```

### 2. Styling Presets
Use predefined presets for common data types:

```python
from report_definitions import StylingPresets

# Get preset styles
header_style = StylingPresets.get_header_style()
percentage_style = StylingPresets.get_percentage_data_style(2)  # 2 decimal places
datetime_style = StylingPresets.get_datetime_data_style()
```

### 3. Column Factory Methods
Create styled columns easily with factory methods:

```python
from report_definitions import ColumnStyleFactory

# Create different types of styled columns
text_col = ColumnStyleFactory.create_text_column('Name', width=30)
number_col = ColumnStyleFactory.create_number_column('Count', width=12, decimal_places=0)
percentage_col = ColumnStyleFactory.create_percentage_column('Rate %', width=15, decimal_places=2)
date_col = ColumnStyleFactory.create_datetime_column('Created Date', width=20)
```

### 4. Report-Wide Default Styling
Set default styling for entire reports:

```python
from report_definitions import ReportDefinition

report = ReportDefinition(
    name='My Report',
    # ... other parameters ...
    default_header_style=StylingPresets.get_header_style("FF0066CC"),  # Custom color
    default_data_style=StylingPresets.get_text_data_style()
)

# Apply defaults to all columns
report.apply_default_styling_to_columns()
```

## Styling Components

### Font Styling
```python
font_style = FontStyle(
    name="Arial",           # Font name
    size=12,               # Font size
    color="FF000000",      # Hex color (ARGB format)
    weight=FontWeight.BOLD, # NORMAL or BOLD
    italic=True,           # Italic text
    underline=False        # Underlined text
)
```

### Fill/Background Styling
```python
fill_style = FillStyle(
    fill_type=FillType.SOLID,    # NONE or SOLID
    start_color="FFCCCCCC",      # Background color (hex ARGB)
    end_color="FFCCCCCC"         # End color for gradients
)
```

### Border Styling
```python
# Individual border
border = BorderStyle(
    style=BorderStyleType.THIN,  # NONE, THIN, MEDIUM, THICK
    color="FF000000"             # Border color (hex ARGB)
)

# All borders at once
borders = CellBorders.all_borders(
    style=BorderStyleType.THIN,
    color="FF000000"
)

# Individual sides
borders = CellBorders(
    left=BorderStyle(style=BorderStyleType.THIN, color="FF000000"),
    right=BorderStyle(style=BorderStyleType.MEDIUM, color="FF0000FF"),
    top=None,    # No top border
    bottom=None  # No bottom border
)
```

### Alignment Styling
```python
alignment = AlignmentStyle(
    horizontal=HorizontalAlignment.CENTER,  # LEFT, CENTER, RIGHT, JUSTIFY
    vertical=VerticalAlignment.CENTER,      # TOP, CENTER, BOTTOM
    wrap_text=True                          # Text wrapping
)
```

## Utility Functions

### Color Utilities
```python
from report_definitions import StylingUtils

# Validate hex colors
is_valid = StylingUtils.validate_hex_color("FF000000")  # True

# Convert RGB to hex
hex_color = StylingUtils.rgb_to_hex(255, 0, 0, 255)  # "FFFF0000"

# Convert hex to RGB
r, g, b, a = StylingUtils.hex_to_rgb("FFFF0000")  # (255, 0, 0, 255)
```

### Style Merging
```python
# Merge two styles (override takes precedence)
merged_style = StylingUtils.merge_styles(base_style, override_style)
```

### Data Type Auto-Styling
```python
# Get appropriate style based on data type
style = StylingUtils.get_data_type_style('float', '0.00')
```

## Backward Compatibility

The enhanced system maintains full backward compatibility:

```python
# Old-style column definition still works
old_column = ColumnDefinition('Name', 'string', width=20, alignment='center')

# Automatically converts to new styling system
assert old_column.data_style.alignment.horizontal == HorizontalAlignment.CENTER
```

## Common Styling Patterns

### 1. Header with Dark Background
```python
header_style = ExcelCellStyle(
    font=FontStyle(color="FFFFFFFF", weight=FontWeight.BOLD),
    fill=FillStyle(fill_type=FillType.SOLID, start_color="FF242424"),
    alignment=AlignmentStyle(horizontal=HorizontalAlignment.CENTER),
    borders=CellBorders.all_borders()
)
```

### 2. Percentage Column
```python
percentage_style = ExcelCellStyle(
    alignment=AlignmentStyle(horizontal=HorizontalAlignment.RIGHT),
    number_format="0.00",
    borders=CellBorders.all_borders()
)
```

### 3. Highlighted Summary Row
```python
summary_style = ExcelCellStyle(
    font=FontStyle(weight=FontWeight.BOLD),
    fill=FillStyle(fill_type=FillType.SOLID, start_color="FFE2E2E2"),
    borders=CellBorders.all_borders()
)
```

## Integration with Reports

The styling system integrates seamlessly with the existing report generation:

1. **Column Definitions**: Enhanced with styling support
2. **Report Definitions**: Support default styling configuration
3. **Excel Export**: Automatically applies styling using openpyxl
4. **Backward Compatibility**: Existing reports continue to work

## Testing

Run the styling tests to validate functionality:

```bash
python test_enhanced_styling.py          # Basic styling tests
python test_styled_report_generation.py  # Integration tests
```

## Color Reference

Common colors in hex ARGB format:
- Black: `FF000000`
- White: `FFFFFFFF`
- Red: `FFFF0000`
- Green: `FF00FF00`
- Blue: `FF0000FF`
- Dark Gray: `FF242424`
- Light Gray: `FFE2E2E2`
- Light Green: `FFB8ED83`

## Best Practices

1. **Use Factory Methods**: Prefer `ColumnStyleFactory` for common column types
2. **Consistent Colors**: Use predefined color constants for consistency
3. **Test Styling**: Always test Excel output to verify styling appears correctly
4. **Performance**: Styling adds minimal overhead to report generation
5. **Maintainability**: Use presets and utilities for easier maintenance

This enhanced styling system provides powerful Excel formatting capabilities while maintaining the simplicity and backward compatibility of the existing POMS Report Generator.
