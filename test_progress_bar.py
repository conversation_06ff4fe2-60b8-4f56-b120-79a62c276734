#!/usr/bin/env python3
"""
Test the progress bar functionality by simulating the GUI progress callback
"""

import time
from independent_report_generator import IndependentReportGenerator

def test_progress_callback():
    """Test the progress callback functionality"""
    
    print("=== TESTING PROGRESS CALLBACK ===")
    
    # Track progress updates
    progress_updates = []
    
    def progress_callback(message):
        timestamp = time.strftime("%H:%M:%S")
        progress_updates.append(f"[{timestamp}] {message}")
        print(f"[{timestamp}] {message}")
        
        # Simulate progress bar updates (like in the GUI)
        if message.startswith("Processing report"):
            try:
                report_part = message.split(":")[0]  # "Processing report 1/3"
                report_num = int(report_part.split()[2].split("/")[0])  # Extract "1" from "1/3"
                total_reports = int(report_part.split()[2].split("/")[1])  # Extract "3" from "1/3"
                progress_percent = ((report_num - 1) / total_reports) * 100
                print(f"    → Progress Bar: {progress_percent:.1f}% (Report {report_num-1}/{total_reports})")
            except (ValueError, IndexError):
                print("    → Progress Bar: Could not parse report number")
        
        elif message.startswith("✓") and "completed" in message:
            print("    → Progress Bar: Report completed, incrementing...")
    
    # Test with multiple reports
    generator = IndependentReportGenerator()
    
    # Test with 2 reports to see progress updates
    test_reports = [
        'System Performance (Response Time)',
        'Customer Service Performance (Telephone)'
    ]
    
    print(f"\nTesting with {len(test_reports)} reports:")
    for i, report in enumerate(test_reports, 1):
        print(f"  {i}. {report}")
    
    print(f"\nStarting generation...")
    print("=" * 60)
    
    result = generator.generate_multiple_reports(test_reports, 2025, 1, progress_callback)
    
    print("=" * 60)
    print(f"\nGeneration completed!")
    print(f"Total reports: {result['total_reports']}")
    print(f"Successful: {result['successful_reports']}")
    print(f"Failed: {result['failed_reports']}")
    print(f"Total time: {result['total_execution_time']:.1f} seconds")
    
    print(f"\n=== PROGRESS UPDATES SUMMARY ===")
    print(f"Total progress messages: {len(progress_updates)}")
    
    # Count different types of messages
    processing_msgs = [msg for msg in progress_updates if "Processing report" in msg]
    completion_msgs = [msg for msg in progress_updates if "completed" in msg]
    
    print(f"'Processing report' messages: {len(processing_msgs)}")
    print(f"'completed' messages: {len(completion_msgs)}")
    
    if len(processing_msgs) == len(test_reports):
        print("✅ Progress tracking: WORKING - Each report triggered a progress update")
    else:
        print("❌ Progress tracking: FAILED - Missing progress updates")
    
    return len(processing_msgs) == len(test_reports)

if __name__ == "__main__":
    success = test_progress_callback()
    print(f"\n{'='*60}")
    if success:
        print("🎉 PROGRESS BAR TEST: PASSED")
        print("The progress bar functionality is working correctly!")
    else:
        print("❌ PROGRESS BAR TEST: FAILED")
        print("The progress bar functionality needs more work.")
