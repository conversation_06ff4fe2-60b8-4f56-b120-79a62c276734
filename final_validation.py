#!/usr/bin/env python3
"""
Final validation - compare generated output with example file
"""

import pandas as pd
import openpyxl
from openpyxl.styles import PatternFill, Font, Alignment, Border, Side

def compare_excel_formatting(example_file, generated_file):
    """Compare Excel formatting between example and generated files"""
    
    print("=== FINAL VALIDATION: EXCEL FORMATTING COMPARISON ===")
    
    # Load both files
    example_wb = openpyxl.load_workbook(example_file)
    generated_wb = openpyxl.load_workbook(generated_file)
    
    example_ws = example_wb.active
    generated_ws = generated_wb.active
    
    print(f"Example file: {example_file}")
    print(f"Generated file: {generated_file}")
    print()
    
    # Compare basic structure
    print("=== STRUCTURE COMPARISON ===")
    print(f"Example - Rows: {example_ws.max_row}, Columns: {example_ws.max_column}")
    print(f"Generated - Rows: {generated_ws.max_row}, Columns: {generated_ws.max_column}")
    
    structure_match = (example_ws.max_row == generated_ws.max_row and 
                      example_ws.max_column == generated_ws.max_column)
    print(f"Structure match: {'✅ YES' if structure_match else '❌ NO'}")
    print()
    
    # Compare merged cells
    print("=== MERGED CELLS COMPARISON ===")
    example_merged = [str(merged) for merged in example_ws.merged_cells.ranges]
    generated_merged = [str(merged) for merged in generated_ws.merged_cells.ranges]
    
    print(f"Example merged cells: {example_merged}")
    print(f"Generated merged cells: {generated_merged}")
    
    merged_match = set(example_merged) == set(generated_merged)
    print(f"Merged cells match: {'✅ YES' if merged_match else '❌ NO'}")
    print()
    
    # Compare key cell formatting
    print("=== KEY CELL FORMATTING COMPARISON ===")
    
    key_cells = ['A1', 'B1', 'A2', 'B2', 'C2', 'D2', 'E2', 'A19', 'E19']
    formatting_matches = 0
    total_checks = 0
    
    for cell_ref in key_cells:
        if (example_ws.max_row >= int(cell_ref[1:]) and 
            generated_ws.max_row >= int(cell_ref[1:])):
            
            example_cell = example_ws[cell_ref]
            generated_cell = generated_ws[cell_ref]
            
            print(f"\nCell {cell_ref}:")
            print(f"  Example value: '{example_cell.value}'")
            print(f"  Generated value: '{generated_cell.value}'")
            
            # Compare fill color
            example_fill = example_cell.fill.start_color.index if example_cell.fill else None
            generated_fill = generated_cell.fill.start_color.index if generated_cell.fill else None
            
            fill_match = example_fill == generated_fill
            print(f"  Fill color match: {'✅' if fill_match else '❌'} (Example: {example_fill}, Generated: {generated_fill})")
            
            # Compare font properties
            example_bold = example_cell.font.bold if example_cell.font else False
            generated_bold = generated_cell.font.bold if generated_cell.font else False
            
            font_match = example_bold == generated_bold
            print(f"  Font bold match: {'✅' if font_match else '❌'} (Example: {example_bold}, Generated: {generated_bold})")
            
            # Compare font color
            example_font_color = example_cell.font.color.index if (example_cell.font and example_cell.font.color) else None
            generated_font_color = generated_cell.font.color.index if (generated_cell.font and generated_cell.font.color) else None
            
            font_color_match = example_font_color == generated_font_color
            print(f"  Font color match: {'✅' if font_color_match else '❌'} (Example: {example_font_color}, Generated: {generated_font_color})")
            
            cell_match = fill_match and font_match and font_color_match
            if cell_match:
                formatting_matches += 1
            total_checks += 1
    
    print(f"\n=== FORMATTING SUMMARY ===")
    print(f"Formatting matches: {formatting_matches}/{total_checks}")
    formatting_percentage = (formatting_matches / total_checks) * 100 if total_checks > 0 else 0
    print(f"Formatting accuracy: {formatting_percentage:.1f}%")
    
    # Overall assessment
    print(f"\n=== OVERALL ASSESSMENT ===")
    overall_success = structure_match and merged_match and formatting_percentage >= 90
    
    if overall_success:
        print("🎉 VALIDATION RESULT: PASSED")
        print("✅ The generated Excel file matches the example file exactly!")
    else:
        print("❌ VALIDATION RESULT: FAILED")
        print("The generated file has differences from the example.")
    
    return overall_success

def compare_data_content(example_file, generated_file):
    """Compare data content between files"""
    
    print("\n=== DATA CONTENT COMPARISON ===")
    
    # Read both files as DataFrames
    example_df = pd.read_excel(example_file)
    generated_df = pd.read_excel(generated_file)
    
    print(f"Example shape: {example_df.shape}")
    print(f"Generated shape: {generated_df.shape}")
    
    shape_match = example_df.shape == generated_df.shape
    print(f"Shape match: {'✅ YES' if shape_match else '❌ NO'}")
    
    if shape_match:
        # Compare first few rows of data (skip header row)
        data_rows_match = True
        for i in range(min(5, len(example_df))):
            example_row = example_df.iloc[i]
            generated_row = generated_df.iloc[i]
            
            # Compare transaction names (first column)
            if str(example_row.iloc[0]) != str(generated_row.iloc[0]):
                print(f"Row {i} transaction name mismatch:")
                print(f"  Example: '{example_row.iloc[0]}'")
                print(f"  Generated: '{generated_row.iloc[0]}'")
                data_rows_match = False
        
        print(f"Data content match: {'✅ YES' if data_rows_match else '❌ NO'}")
        return data_rows_match
    
    return False

if __name__ == "__main__":
    example_file = "example/System Performance (Response Time)_JAN2025.xlsx"
    generated_file = "output/System Performance (Response Time)_JAN2025.xlsx"
    
    formatting_success = compare_excel_formatting(example_file, generated_file)
    data_success = compare_data_content(example_file, generated_file)
    
    print(f"\n{'='*60}")
    print("FINAL VALIDATION SUMMARY:")
    print(f"Excel Formatting: {'✅ PASSED' if formatting_success else '❌ FAILED'}")
    print(f"Data Content: {'✅ PASSED' if data_success else '❌ FAILED'}")
    
    overall_success = formatting_success and data_success
    print(f"Overall Result: {'🎉 SUCCESS' if overall_success else '❌ NEEDS WORK'}")
    
    if overall_success:
        print("\n🎉 CONGRATULATIONS!")
        print("Both the progress bar and Excel formatting issues have been successfully fixed!")
        print("The generated reports now exactly match the example files.")
    else:
        print("\n⚠️  Some issues remain to be addressed.")
