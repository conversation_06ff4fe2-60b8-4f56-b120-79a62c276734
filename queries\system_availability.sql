-- System Availability (Infra)
-- Generated from: knowage\System Availability (Infra).rptdesign
-- Parameters: year, month
-- Database: cdc_poms

SELECT a.*, (CASE WHEN a.host_group = 'portal' 
AND a.counter >= 18 THEN 'YES' WHEN a.host_group = 'bpm' 
AND a.counter >= 20 THEN 'YES' WHEN a.host_group = 'database' 
AND a.counter >= 7 THEN 'YES' WHEN a.host_group = 'web' 
AND a.counter >= 10 THEN 'YES' WHEN a.host_group = 'network' 
AND a.counter >= 1 THEN 'YES' WHEN a.host_group = 'sso' 
AND a.counter >= 14 THEN 'YES' WHEN a.host_group = 'solr' 
AND a.counter >= 2 THEN 'YES' ELSE 'NO' END) AS all_down_flag 
FROM ( 
SELECT host_group, DATE_FORMAT(updated_at, '%Y-%m-%d %H:%i') AS downtime, COUNT(1) AS counter 
FROM cdc_poms.sla_nagios 
WHERE host_group IN ('sso', 'portal', 'database', 'bpm', 'solr', 'web', 'network') 
AND is_exclude = 0 
AND service_status <> '2' 
AND MONTH(updated_at) = ? 
AND YEAR (updated_at) = ? 
AND ( DAYOFWEEK(updated_at) NOT IN (6, 7) 
OR ( DAYOFWEEK(updated_at) IN (6) 
AND TIME(updated_at) NOT BETWEEN TIME('22:00:00') 
AND TIME('23:59:00') ) 
OR ( DAYOFWEEK(updated_at) IN (7) 
AND TIME(updated_at) NOT BETWEEN TIME('00:00:00') 
AND TIME('06:00:00') ) ) 
GROUP BY host_group, DATE_FORMAT(updated_at, '%Y-%m-%d %H:%i') ) a 
ORDER BY 2, 1;;
