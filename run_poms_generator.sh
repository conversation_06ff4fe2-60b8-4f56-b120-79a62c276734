#!/bin/bash
# POMS Report Generator - Linux/macOS Shell Launcher
# This script provides an easy way to run the POMS Report Generator on Linux/macOS

echo "========================================"
echo "POMS Report Generator"
echo "========================================"
echo

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "ERROR: Python 3 is not installed or not in PATH"
    echo "Please install Python 3.8 or higher"
    exit 1
fi

# Check if required files exist
if [ ! -f "main.py" ]; then
    echo "ERROR: main.py not found"
    echo "Please ensure you are running this script from the correct directory"
    exit 1
fi

if [ ! -f "config.yaml" ]; then
    echo "ERROR: config.yaml not found"
    echo "Please ensure the configuration file exists"
    exit 1
fi

# Check if dependencies are installed
echo "Checking dependencies..."
python3 -c "import pandas, mysql.connector, yaml, ttkbootstrap" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "Installing required dependencies..."
    pip3 install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "ERROR: Failed to install dependencies"
        echo "Please run: pip3 install -r requirements.txt"
        exit 1
    fi
fi

# Create necessary directories
mkdir -p output logs

echo "Dependencies OK"
echo

# Function to show menu
show_menu() {
    echo "Choose an option:"
    echo "1. Run GUI Application (Recommended)"
    echo "2. List Available Reports"
    echo "3. Test Database Connections"
    echo "4. Run Validation Tests"
    echo "5. Generate Reports (Command Line)"
    echo "6. Exit"
    echo
}

# Main menu loop
while true; do
    show_menu
    read -p "Enter your choice (1-6): " choice
    
    case $choice in
        1)
            echo
            echo "Starting GUI Application..."
            python3 main.py
            break
            ;;
        2)
            echo
            echo "Available Reports:"
            python3 main.py --cli --list-reports
            echo
            read -p "Press Enter to continue..."
            ;;
        3)
            echo
            echo "Testing Database Connections..."
            python3 main.py --cli --test-connections
            echo
            read -p "Press Enter to continue..."
            ;;
        4)
            echo
            echo "Running Validation Tests..."
            python3 main.py --cli --validate
            echo
            read -p "Press Enter to continue..."
            ;;
        5)
            echo
            echo "Command Line Report Generation"
            echo
            read -p "Enter year (e.g., 2025): " year
            read -p "Enter month (1-12): " month
            echo
            echo "Choose reports to generate:"
            echo "1. All reports"
            echo "2. Specific reports"
            read -p "Enter choice (1-2): " report_choice
            
            if [ "$report_choice" = "1" ]; then
                echo
                echo "Generating all reports for $month/$year..."
                python3 main.py --cli --generate --all-reports --year $year --month $month
            elif [ "$report_choice" = "2" ]; then
                echo
                echo "Available reports:"
                python3 main.py --cli --list-reports
                echo
                echo "Enter report names separated by spaces (use quotes for names with spaces):"
                read -p "Reports: " reports
                echo
                echo "Generating selected reports for $month/$year..."
                python3 main.py --cli --generate --reports $reports --year $year --month $month
            else
                echo "Invalid choice."
            fi
            echo
            read -p "Press Enter to continue..."
            ;;
        6)
            echo
            echo "Goodbye!"
            break
            ;;
        *)
            echo "Invalid choice. Please try again."
            ;;
    esac
done

echo
echo "Script completed."
