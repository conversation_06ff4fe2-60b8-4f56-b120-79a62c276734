#!/usr/bin/env python3
"""
POMS Report Generator - Main Entry Point

This is the main entry point for the POMS Report Generator application.
It provides both GUI and command-line interfaces.
"""

import sys
import argparse
import logging
from pathlib import Path
import os

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from gui_application import PomsReportGeneratorGUI
from independent_report_generator import IndependentReportGenerator
from database_manager import DatabaseManager
from validation_testing import ReportValidator

def setup_logging(log_level: str = "INFO"):
    """Setup logging configuration"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / 'poms_report_generator.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def run_gui():
    """Run the GUI application"""
    try:
        app = PomsReportGeneratorGUI()
        app.run()
    except Exception as e:
        logging.error(f"GUI application failed: {str(e)}")
        print(f"Error: {str(e)}")
        sys.exit(1)

def run_cli(args):
    """Run command-line interface"""
    try:
        generator = IndependentReportGenerator()
        
        if args.list_reports:
            print("Available reports:")
            for report_name in generator.get_available_reports():
                print(f"  - {report_name}")
            return
        
        if args.test_connections:
            print("Testing database connections...")
            results = generator.db_manager.test_all_connections()
            for db_name, status in results.items():
                status_text = "✓ SUCCESS" if status else "✗ FAILED"
                print(f"  {db_name}: {status_text}")
            return
        
        if args.validate:
            print("Running validation tests...")
            validator = ReportValidator()
            results = validator.validate_all_reports()
            validator.print_validation_summary(results)
            return
        
        if args.generate:
            if not args.year or not args.month:
                print("Error: --year and --month are required for report generation")
                sys.exit(1)
            
            year = int(args.year)
            month = int(args.month)
            
            # Determine reports to generate
            if args.reports:
                report_names = args.reports
            elif args.all_reports:
                report_names = generator.get_available_reports()
            else:
                print("Error: Specify --reports or --all-reports")
                sys.exit(1)
            
            # Validate report names
            available_reports = generator.get_available_reports()
            invalid_reports = [r for r in report_names if r not in available_reports]
            if invalid_reports:
                print(f"Error: Invalid report names: {invalid_reports}")
                print("Use --list-reports to see available reports")
                sys.exit(1)
            
            # Set output directory if specified
            if args.output_dir:
                generator.output_dir = Path(args.output_dir)
                generator.output_dir.mkdir(exist_ok=True)
            
            print(f"Generating {len(report_names)} reports for {month:02d}/{year}...")
            
            def progress_callback(message):
                print(f"  {message}")
            
            results = generator.generate_multiple_reports(
                report_names, year, month, progress_callback
            )
            
            # Print summary
            print(f"\nGeneration completed:")
            print(f"  Successful: {results['successful_reports']}")
            print(f"  Failed: {results['failed_reports']}")
            print(f"  Total time: {results['total_execution_time']:.1f} seconds")
            
            if results['failed_reports'] > 0:
                print("\nFailed reports:")
                for result in results['results']:
                    if not result['success']:
                        print(f"  - {result['report_name']}: {result['error_message']}")
    
    except Exception as e:
        logging.error(f"CLI execution failed: {str(e)}")
        print(f"Error: {str(e)}")
        sys.exit(1)

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="POMS Report Generator - Generate Excel reports from SQL queries with modern formatting",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run GUI application
  python main.py

  # List available reports
  python main.py --list-reports

  # Test database connections
  python main.py --test-connections

  # Generate all reports for January 2025
  python main.py --generate --all-reports --year 2025 --month 1

  # Generate specific reports
  python main.py --generate --reports "Service Request" "System Availability (Infra)" --year 2025 --month 1

  # Run validation tests
  python main.py --validate
        """
    )
    
    # Mode selection
    parser.add_argument('--gui', action='store_true', default=True,
                       help='Run GUI application (default)')
    parser.add_argument('--cli', action='store_true',
                       help='Run in command-line mode')
    
    # CLI-specific options
    parser.add_argument('--list-reports', action='store_true',
                       help='List all available reports')
    parser.add_argument('--test-connections', action='store_true',
                       help='Test database connections')
    parser.add_argument('--validate', action='store_true',
                       help='Run validation tests')
    parser.add_argument('--generate', action='store_true',
                       help='Generate reports')
    
    # Generation parameters
    parser.add_argument('--year', type=int,
                       help='Year for report generation (required with --generate)')
    parser.add_argument('--month', type=int,
                       help='Month for report generation (required with --generate)')
    parser.add_argument('--reports', nargs='+',
                       help='Specific reports to generate')
    parser.add_argument('--all-reports', action='store_true',
                       help='Generate all available reports')
    parser.add_argument('--output-dir', 
                       help='Output directory for generated reports')
    
    # Logging
    parser.add_argument('--log-level', default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='Logging level')
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level)
    
    # Determine mode
    cli_mode = (args.cli or args.list_reports or args.test_connections or 
                args.validate or args.generate)
    
    if cli_mode:
        run_cli(args)
    else:
        run_gui()

if __name__ == "__main__":
    main()
