"""
POMS Report Generator GUI Application

Modern GUI application for generating POMS reports using independent SQL-based architecture.
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import threading
import logging
from datetime import datetime
from pathlib import Path
import os
import sys

from independent_report_generator import IndependentReportGenerator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('poms_report_generator.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class PomsReportGeneratorGUI:
    """Main GUI application class"""
    
    def __init__(self):
        """Initialize the GUI application"""
        self.root = ttk_bs.Window(themename="cosmo")
        self.root.title("POMS Report Generator")
        self.root.geometry("800x700")
        self.root.resizable(True, True)
        
        # Initialize report generator
        try:
            self.report_generator = IndependentReportGenerator()
            self.available_reports = self.report_generator.get_available_reports()
        except Exception as e:
            messagebox.showerror("Initialization Error",
                               f"Failed to initialize report generator:\n{str(e)}")
            self.root.destroy()
            return
        
        # GUI state variables
        self.report_vars = {}
        self.all_reports_var = tk.BooleanVar()
        self.year_var = tk.StringVar(value=str(datetime.now().year))
        self.month_var = tk.StringVar(value=str(datetime.now().month))
        self.output_dir_var = tk.StringVar(value="./output")
        self.generation_in_progress = False
        
        self.setup_gui()
        self.center_window()
    
    def setup_gui(self):
        """Set up the GUI components"""
        # Main container
        main_frame = ttk_bs.Frame(self.root, padding=20)
        main_frame.pack(fill=BOTH, expand=True)
        
        # Title
        title_label = ttk_bs.Label(
            main_frame, 
            text="POMS Report Generator", 
            font=("Arial", 18, "bold"),
            bootstyle=PRIMARY
        )
        title_label.pack(pady=(0, 20))
        
        # Parameters section
        self.create_parameters_section(main_frame)
        
        # Reports selection section
        self.create_reports_section(main_frame)
        
        # Output section
        self.create_output_section(main_frame)
        
        # Control buttons
        self.create_control_buttons(main_frame)
        
        # Progress section
        self.create_progress_section(main_frame)
        
        # Status bar
        self.create_status_bar()
    
    def create_parameters_section(self, parent):
        """Create the parameters input section"""
        params_frame = ttk_bs.LabelFrame(parent, text="Report Parameters", padding=15)
        params_frame.pack(fill=X, pady=(0, 15))
        
        # Year and Month in a row
        params_row = ttk_bs.Frame(params_frame)
        params_row.pack(fill=X)
        
        # Year
        ttk_bs.Label(params_row, text="Year:").pack(side=LEFT, padx=(0, 5))
        year_spinbox = ttk_bs.Spinbox(
            params_row, 
            from_=2000, 
            to=datetime.now().year + 1,
            textvariable=self.year_var,
            width=10,
            state="readonly"
        )
        year_spinbox.pack(side=LEFT, padx=(0, 20))
        
        # Month
        ttk_bs.Label(params_row, text="Month:").pack(side=LEFT, padx=(0, 5))
        month_combo = ttk_bs.Combobox(
            params_row,
            textvariable=self.month_var,
            values=[f"{i:02d}" for i in range(1, 13)],
            width=8,
            state="readonly"
        )
        month_combo.pack(side=LEFT)
        
        # Month names for reference
        month_names = ["Jan", "Feb", "Mar", "Apr", "May", "Jun",
                      "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
        month_label = ttk_bs.Label(
            params_row, 
            text=f"({', '.join([f'{i+1:02d}={name}' for i, name in enumerate(month_names)])})",
            font=("Arial", 8)
        )
        month_label.pack(side=LEFT, padx=(10, 0))
    
    def create_reports_section(self, parent):
        """Create the reports selection section"""
        reports_frame = ttk_bs.LabelFrame(parent, text="Select Reports", padding=15)
        reports_frame.pack(fill=BOTH, expand=True, pady=(0, 15))
        
        # All reports checkbox
        all_reports_cb = ttk_bs.Checkbutton(
            reports_frame,
            text="Select All Reports",
            variable=self.all_reports_var,
            command=self.toggle_all_reports,
            bootstyle="success-round-toggle"
        )
        all_reports_cb.pack(anchor=W, pady=(0, 10))
        
        # Scrollable frame for individual reports
        canvas = tk.Canvas(reports_frame, height=200)
        scrollbar = ttk_bs.Scrollbar(reports_frame, orient=VERTICAL, command=canvas.yview)
        scrollable_frame = ttk_bs.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # Create checkboxes for each report
        for report_name in self.available_reports:
            var = tk.BooleanVar()
            self.report_vars[report_name] = var
            
            cb = ttk_bs.Checkbutton(
                scrollable_frame,
                text=report_name,
                variable=var,
                command=self.update_all_reports_checkbox
            )
            cb.pack(anchor=W, pady=2)
        
        canvas.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
    
    def create_output_section(self, parent):
        """Create the output directory section"""
        output_frame = ttk_bs.LabelFrame(parent, text="Output Settings", padding=15)
        output_frame.pack(fill=X, pady=(0, 15))
        
        output_row = ttk_bs.Frame(output_frame)
        output_row.pack(fill=X)
        
        ttk_bs.Label(output_row, text="Output Directory:").pack(side=LEFT, padx=(0, 10))
        
        output_entry = ttk_bs.Entry(output_row, textvariable=self.output_dir_var)
        output_entry.pack(side=LEFT, fill=X, expand=True, padx=(0, 10))
        
        browse_btn = ttk_bs.Button(
            output_row,
            text="Browse",
            command=self.browse_output_directory,
            bootstyle=SECONDARY
        )
        browse_btn.pack(side=RIGHT)
    
    def create_control_buttons(self, parent):
        """Create control buttons"""
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.pack(fill=X, pady=(0, 15))
        
        # Generate button
        self.generate_btn = ttk_bs.Button(
            buttons_frame,
            text="Generate Reports",
            command=self.start_generation,
            bootstyle="success",
            width=20
        )
        self.generate_btn.pack(side=LEFT, padx=(0, 10))
        
        # Cancel button
        self.cancel_btn = ttk_bs.Button(
            buttons_frame,
            text="Cancel",
            command=self.cancel_generation,
            bootstyle="danger",
            width=15,
            state=DISABLED
        )
        self.cancel_btn.pack(side=LEFT, padx=(0, 10))
        
        # Test connections button
        test_btn = ttk_bs.Button(
            buttons_frame,
            text="Test Database Connections",
            command=self.test_connections,
            bootstyle="info",
            width=25
        )
        test_btn.pack(side=RIGHT)
    
    def create_progress_section(self, parent):
        """Create progress tracking section"""
        progress_frame = ttk_bs.LabelFrame(parent, text="Progress", padding=15)
        progress_frame.pack(fill=X, pady=(0, 15))
        
        # Overall progress bar
        self.overall_progress = ttk_bs.Progressbar(
            progress_frame,
            mode='determinate',
            bootstyle="success-striped"
        )
        self.overall_progress.pack(fill=X, pady=(0, 10))
        
        # Progress text area
        self.progress_text = tk.Text(
            progress_frame,
            height=8,
            wrap=tk.WORD,
            font=("Consolas", 9)
        )
        
        progress_scrollbar = ttk_bs.Scrollbar(progress_frame, command=self.progress_text.yview)
        self.progress_text.configure(yscrollcommand=progress_scrollbar.set)
        
        self.progress_text.pack(side=LEFT, fill=BOTH, expand=True)
        progress_scrollbar.pack(side=RIGHT, fill=Y)
    
    def create_status_bar(self):
        """Create status bar"""
        self.status_bar = ttk_bs.Label(
            self.root,
            text="Ready",
            relief=tk.SUNKEN,
            anchor=W,
            padding=(10, 5)
        )
        self.status_bar.pack(side=BOTTOM, fill=X)
    
    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def toggle_all_reports(self):
        """Toggle all report checkboxes"""
        select_all = self.all_reports_var.get()
        for var in self.report_vars.values():
            var.set(select_all)
    
    def update_all_reports_checkbox(self):
        """Update the 'Select All' checkbox based on individual selections"""
        selected_count = sum(1 for var in self.report_vars.values() if var.get())
        total_count = len(self.report_vars)

        if selected_count == 0:
            self.all_reports_var.set(False)
        elif selected_count == total_count:
            self.all_reports_var.set(True)
        else:
            # Partial selection - could implement indeterminate state here
            self.all_reports_var.set(False)

    def browse_output_directory(self):
        """Browse for output directory"""
        directory = filedialog.askdirectory(
            title="Select Output Directory",
            initialdir=self.output_dir_var.get()
        )
        if directory:
            self.output_dir_var.set(directory)

    def get_selected_reports(self):
        """Get list of selected report names"""
        return [name for name, var in self.report_vars.items() if var.get()]

    def validate_inputs(self):
        """Validate user inputs"""
        try:
            year = int(self.year_var.get())
            month = int(self.month_var.get())
        except ValueError:
            messagebox.showerror("Invalid Input", "Year and month must be valid numbers")
            return False

        if year < 2000 or year > datetime.now().year + 1:
            messagebox.showerror("Invalid Year", f"Year must be between 2000 and {datetime.now().year + 1}")
            return False

        if month < 1 or month > 12:
            messagebox.showerror("Invalid Month", "Month must be between 1 and 12")
            return False

        selected_reports = self.get_selected_reports()
        if not selected_reports:
            messagebox.showerror("No Reports Selected", "Please select at least one report to generate")
            return False

        output_dir = self.output_dir_var.get()
        if not output_dir:
            messagebox.showerror("No Output Directory", "Please specify an output directory")
            return False

        return True

    def update_progress_text(self, message):
        """Update progress text area"""
        self.progress_text.insert(tk.END, f"{datetime.now().strftime('%H:%M:%S')} - {message}\n")
        self.progress_text.see(tk.END)
        self.root.update_idletasks()

    def update_status(self, message):
        """Update status bar"""
        self.status_bar.config(text=message)
        self.root.update_idletasks()

    def test_connections(self):
        """Test database connections"""
        self.update_status("Testing database connections...")
        self.update_progress_text("Testing database connections...")

        def test_thread():
            try:
                results = self.report_generator.db_manager.test_all_connections()

                self.root.after(0, lambda: self.update_progress_text("Database Connection Results:"))
                for db_name, status in results.items():
                    status_text = "✓ SUCCESS" if status else "✗ FAILED"
                    self.root.after(0, lambda db=db_name, st=status_text:
                                  self.update_progress_text(f"  {db}: {st}"))

                success_count = sum(1 for status in results.values() if status)
                total_count = len(results)

                self.root.after(0, lambda: self.update_status(
                    f"Database test completed: {success_count}/{total_count} connections successful"))

            except Exception as e:
                self.root.after(0, lambda: self.update_progress_text(f"Connection test error: {str(e)}"))
                self.root.after(0, lambda: self.update_status("Connection test failed"))

        threading.Thread(target=test_thread, daemon=True).start()

    def start_generation(self):
        """Start report generation process"""
        if not self.validate_inputs():
            return

        if self.generation_in_progress:
            messagebox.showwarning("Generation in Progress", "Report generation is already in progress")
            return

        # Get parameters
        year = int(self.year_var.get())
        month = int(self.month_var.get())
        selected_reports = self.get_selected_reports()
        output_dir = self.output_dir_var.get()

        # Update output directory in report generator
        self.report_generator.output_dir = Path(output_dir)
        self.report_generator.output_dir.mkdir(exist_ok=True)

        # Update UI state
        self.generation_in_progress = True
        self.generate_btn.config(state=DISABLED)
        self.cancel_btn.config(state=NORMAL)

        # Reset progress
        self.overall_progress['value'] = 0
        self.overall_progress['maximum'] = len(selected_reports)
        self.progress_text.delete(1.0, tk.END)

        self.update_status(f"Generating {len(selected_reports)} reports...")
        self.update_progress_text(f"Starting generation of {len(selected_reports)} reports for {month:02d}/{year}")

        # Start generation in separate thread
        def generation_thread():
            try:
                current_report_index = [0]  # Use list to allow modification in nested function

                def progress_callback(message):
                    self.root.after(0, lambda: self.update_progress_text(message))

                    # Update progress bar when starting a new report
                    if message.startswith("Processing report"):
                        # Extract report number from message like "Processing report 1/3: Report Name"
                        try:
                            report_part = message.split(":")[0]  # "Processing report 1/3"
                            report_num = int(report_part.split()[2].split("/")[0])  # Extract "1" from "1/3"
                            current_report_index[0] = report_num - 1  # Convert to 0-based index
                            self.root.after(0, lambda: setattr(self.overall_progress, 'value', current_report_index[0]))
                        except (ValueError, IndexError):
                            pass  # If parsing fails, just continue

                    # Update progress bar when a report completes
                    elif message.startswith("✓") and "completed" in message:
                        current_report_index[0] += 1
                        self.root.after(0, lambda: setattr(self.overall_progress, 'value', current_report_index[0]))

                result = self.report_generator.generate_multiple_reports(
                    selected_reports, year, month, progress_callback
                )

                # Ensure progress bar shows completion
                self.root.after(0, lambda: setattr(self.overall_progress, 'value', len(selected_reports)))

                # Show completion message
                self.root.after(0, lambda: self.generation_completed(result))

            except Exception as e:
                self.root.after(0, lambda: self.generation_error(str(e)))

        threading.Thread(target=generation_thread, daemon=True).start()

    def cancel_generation(self):
        """Cancel report generation (placeholder - actual cancellation would need more complex implementation)"""
        if messagebox.askyesno("Cancel Generation", "Are you sure you want to cancel report generation?"):
            self.generation_in_progress = False
            self.generate_btn.config(state=NORMAL)
            self.cancel_btn.config(state=DISABLED)
            self.update_status("Generation cancelled")
            self.update_progress_text("Generation cancelled by user")

    def generation_completed(self, result):
        """Handle completion of report generation"""
        self.generation_in_progress = False
        self.generate_btn.config(state=NORMAL)
        self.cancel_btn.config(state=DISABLED)

        # Show summary
        successful = result['successful_reports']
        failed = result['failed_reports']
        total_time = result['total_execution_time']

        summary_msg = (f"Generation completed!\n\n"
                      f"Successful: {successful}\n"
                      f"Failed: {failed}\n"
                      f"Total time: {total_time:.1f} seconds")

        self.update_progress_text("=" * 50)
        self.update_progress_text("GENERATION SUMMARY:")
        self.update_progress_text(f"Successful reports: {successful}")
        self.update_progress_text(f"Failed reports: {failed}")
        self.update_progress_text(f"Total execution time: {total_time:.1f} seconds")

        if failed > 0:
            self.update_progress_text("\nFailed reports:")
            for report_result in result['results']:
                if not report_result['success']:
                    self.update_progress_text(f"  - {report_result['report_name']}: {report_result['error_message']}")

        self.update_status(f"Completed: {successful} successful, {failed} failed")

        if successful > 0:
            if messagebox.askyesno("Generation Complete",
                                 f"{summary_msg}\n\nWould you like to open the output directory?"):
                self.open_output_directory()

    def generation_error(self, error_message):
        """Handle generation error"""
        self.generation_in_progress = False
        self.generate_btn.config(state=NORMAL)
        self.cancel_btn.config(state=DISABLED)

        self.update_progress_text(f"GENERATION ERROR: {error_message}")
        self.update_status("Generation failed")

        messagebox.showerror("Generation Error", f"Report generation failed:\n\n{error_message}")

    def open_output_directory(self):
        """Open the output directory in file explorer"""
        output_path = Path(self.output_dir_var.get())
        if output_path.exists():
            if sys.platform == "win32":
                os.startfile(output_path)
            elif sys.platform == "darwin":
                os.system(f"open '{output_path}'")
            else:
                os.system(f"xdg-open '{output_path}'")

    def run(self):
        """Run the GUI application"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            logger.info("Application interrupted by user")
        except Exception as e:
            logger.error(f"Application error: {str(e)}")
            messagebox.showerror("Application Error", f"An unexpected error occurred:\n{str(e)}")


def main():
    """Main entry point"""
    try:
        app = PomsReportGeneratorGUI()
        app.run()
    except Exception as e:
        logger.error(f"Failed to start application: {str(e)}")
        messagebox.showerror("Startup Error", f"Failed to start application:\n{str(e)}")


if __name__ == "__main__":
    main()
