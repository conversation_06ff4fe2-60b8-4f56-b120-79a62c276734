#!/usr/bin/env python3
"""
Test Styled Report Generation

This script tests generating an actual report with the enhanced styling system.
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from independent_report_generator import IndependentReportGenerator
from report_definitions import REPORT_REGISTRY


def test_styled_report_generation():
    """Test generating a report with enhanced styling"""
    print("=== Testing Styled Report Generation ===")
    
    # Initialize the report generator
    generator = IndependentReportGenerator()
    
    # Test with Customer Service Performance (Telephone) which now uses styled columns
    report_name = 'Customer Service Performance (Telephone)'
    
    try:
        print(f"Generating report: {report_name}")
        result = generator.generate_single_report(report_name, 2025, 6)
        
        if result['success']:
            print(f"✓ Report generated successfully: {result['filename']}")
            print(f"  Rows: {result['rows_generated']}")
            print(f"  Execution time: {result['execution_time']:.2f}s")
            
            # Check if the file exists and has proper styling
            output_file = Path('output') / result['filename']
            if output_file.exists():
                print(f"✓ Output file exists: {output_file}")
                
                # Validate the Excel file has proper styling
                import openpyxl
                wb = openpyxl.load_workbook(output_file)
                ws = wb.active
                
                # Check header styling
                header_cell = ws['A1']
                if header_cell.font.bold:
                    print("✓ Header has bold formatting")
                else:
                    print("⚠ Header formatting may not be applied correctly")
                
                # Check percentage column formatting
                if len(ws['G']) > 1:  # ABANDON % column
                    pct_cell = ws['G2']
                    if pct_cell.number_format in ['0.00', '0.00%']:
                        print("✓ Percentage column has proper number formatting")
                    else:
                        print(f"⚠ Percentage formatting: {pct_cell.number_format}")
                
                wb.close()
                return True
            else:
                print(f"❌ Output file not found: {output_file}")
                return False
        else:
            print(f"❌ Report generation failed: {result.get('error_message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Exception during report generation: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_report_definition_styling():
    """Test that report definitions have proper styling"""
    print("\n=== Testing Report Definition Styling ===")
    
    # Get the updated report definition
    report_def = REPORT_REGISTRY.get_report('Customer Service Performance (Telephone)')
    
    if not report_def:
        print("❌ Report definition not found")
        return False
    
    print(f"Report: {report_def.name}")
    print(f"Columns: {len(report_def.columns)}")
    
    # Check that columns have styling
    styled_columns = 0
    for col in report_def.columns:
        if col.header_style is not None and col.data_style is not None:
            styled_columns += 1
            print(f"  ✓ {col.name}: has header and data styling")
        else:
            print(f"  ⚠ {col.name}: missing styling")
    
    if styled_columns == len(report_def.columns):
        print(f"✓ All {styled_columns} columns have proper styling")
        return True
    else:
        print(f"⚠ Only {styled_columns}/{len(report_def.columns)} columns have styling")
        return False


def test_system_performance_styling():
    """Test System Performance report with custom styling"""
    print("\n=== Testing System Performance Styling ===")
    
    report_def = REPORT_REGISTRY.get_report('System Performance (Response Time)')
    
    if not report_def:
        print("❌ System Performance report definition not found")
        return False
    
    print(f"Report: {report_def.name}")
    
    # Check first column has special header styling
    first_col = report_def.columns[0]
    if first_col.header_style and first_col.header_style.fill:
        header_color = first_col.header_style.fill.start_color
        if header_color == "FF2C2C2C":  # Special dark gray
            print("✓ First column has special header styling")
        else:
            print(f"⚠ First column header color: {header_color}")
    
    # Check other columns have highlight styling
    highlight_columns = 0
    for col in report_def.columns[1:]:
        if col.header_style and col.header_style.fill:
            if col.header_style.fill.start_color == "FFB8ED83":  # Light green
                highlight_columns += 1
    
    if highlight_columns > 0:
        print(f"✓ {highlight_columns} columns have highlight styling")
        return True
    else:
        print("⚠ No columns have highlight styling")
        return False


def main():
    """Run all styling tests"""
    print("Testing Enhanced Styling with Actual Reports")
    print("=" * 50)
    
    success = True
    
    try:
        # Test report definition styling
        if not test_report_definition_styling():
            success = False
        
        # Test System Performance styling
        if not test_system_performance_styling():
            success = False
        
        # Test actual report generation
        if not test_styled_report_generation():
            success = False
        
        print("\n" + "=" * 50)
        if success:
            print("🎉 ALL STYLING TESTS PASSED!")
            print("Enhanced styling system works with actual reports.")
        else:
            print("⚠ SOME TESTS HAD ISSUES")
            print("Check the output above for details.")
        
    except Exception as e:
        print(f"\n❌ TEST SUITE FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
        success = False
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
