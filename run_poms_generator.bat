@echo off
REM POMS Report Generator - Windows Batch Launcher
REM This script provides an easy way to run the POMS Report Generator on Windows

echo ========================================
echo POMS Report Generator
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8 or higher from https://python.org
    pause
    exit /b 1
)

REM Check if required files exist
if not exist "main.py" (
    echo ERROR: main.py not found
    echo Please ensure you are running this script from the correct directory
    pause
    exit /b 1
)

if not exist "config.yaml" (
    echo ERROR: config.yaml not found
    echo Please ensure the configuration file exists
    pause
    exit /b 1
)

REM Check if dependencies are installed
echo Checking dependencies...
python -c "import pandas, mysql.connector, yaml, ttkbootstrap" >nul 2>&1
if errorlevel 1 (
    echo Installing required dependencies...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies
        echo Please run: pip install -r requirements.txt
        pause
        exit /b 1
    )
)

REM Create necessary directories
if not exist "output" mkdir output
if not exist "logs" mkdir logs

echo Dependencies OK
echo.

REM Show menu
:menu
echo Choose an option:
echo 1. Run GUI Application (Recommended)
echo 2. List Available Reports
echo 3. Test Database Connections
echo 4. Run Validation Tests
echo 5. Generate Reports (Command Line)
echo 6. Exit
echo.
set /p choice="Enter your choice (1-6): "

if "%choice%"=="1" goto gui
if "%choice%"=="2" goto list_reports
if "%choice%"=="3" goto test_connections
if "%choice%"=="4" goto validate
if "%choice%"=="5" goto generate_cli
if "%choice%"=="6" goto exit
echo Invalid choice. Please try again.
goto menu

:gui
echo.
echo Starting GUI Application...
python main.py
goto end

:list_reports
echo.
echo Available Reports:
python main.py --cli --list-reports
echo.
pause
goto menu

:test_connections
echo.
echo Testing Database Connections...
python main.py --cli --test-connections
echo.
pause
goto menu

:validate
echo.
echo Running Validation Tests...
python main.py --cli --validate
echo.
pause
goto menu

:generate_cli
echo.
echo Command Line Report Generation
echo.
set /p year="Enter year (e.g., 2025): "
set /p month="Enter month (1-12): "
echo.
echo Choose reports to generate:
echo 1. All reports
echo 2. Specific reports (you'll be prompted)
set /p report_choice="Enter choice (1-2): "

if "%report_choice%"=="1" (
    echo.
    echo Generating all reports for %month%/%year%...
    python main.py --cli --generate --all-reports --year %year% --month %month%
) else if "%report_choice%"=="2" (
    echo.
    echo Available reports:
    python main.py --cli --list-reports
    echo.
    echo Enter report names separated by spaces (use quotes for names with spaces):
    set /p reports="Reports: "
    echo.
    echo Generating selected reports for %month%/%year%...
    python main.py --cli --generate --reports %reports% --year %year% --month %month%
) else (
    echo Invalid choice.
)
echo.
pause
goto menu

:exit
echo.
echo Goodbye!
goto end

:end
echo.
echo Press any key to exit...
pause >nul
