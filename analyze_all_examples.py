#!/usr/bin/env python3
"""
Analyze all example files to understand formatting requirements
"""

import pandas as pd
import os
from pathlib import Path

def analyze_example_file(filepath):
    """Analyze a single example file"""
    try:
        df = pd.read_excel(filepath)
        print(f"\n=== {os.path.basename(filepath)} ===")
        print(f"Shape: {df.shape}")
        print(f"Columns: {list(df.columns)}")
        print("First few rows:")
        print(df.head())
        print("Data types:")
        print(df.dtypes)
        
        # Check for numeric columns that might need decimal formatting
        numeric_cols = df.select_dtypes(include=['float64', 'int64']).columns
        if len(numeric_cols) > 0:
            print(f"Numeric columns: {list(numeric_cols)}")
            for col in numeric_cols:
                print(f"  {col} sample values: {df[col].head().tolist()}")
        
        return True
    except Exception as e:
        print(f"Error analyzing {filepath}: {e}")
        return False

def main():
    example_dir = Path("Example")
    
    if not example_dir.exists():
        print("Example directory not found!")
        return
    
    excel_files = list(example_dir.glob("*.xlsx"))
    excel_files = [f for f in excel_files if not f.name.startswith("~$")]  # Skip temp files
    
    print(f"Found {len(excel_files)} example files:")
    for f in excel_files:
        print(f"  - {f.name}")
    
    print("\n" + "="*80)
    print("DETAILED ANALYSIS")
    print("="*80)
    
    for filepath in excel_files:
        analyze_example_file(filepath)

if __name__ == "__main__":
    main()
