# POMS Report Generator

A professional Python application that generates POMS reports using a modern SQL-based architecture with independent query files and enhanced formatting, featuring a modern GUI interface.

## Features

- **Independent SQL Architecture**: Uses individual SQL query files from the `queries/` subdirectory for maximum maintainability
- **Modern GUI Interface**: User-friendly interface built with ttkbootstrap for professional appearance
- **MySQL Database Integration**: Supports MySQL database connections as specified in user preferences
- **Excel Output**: Generates Excel files with report-specific formatting classes for exact output matching
- **Progress Tracking**: Real-time progress indicators and detailed logging
- **Validation System**: Compares generated reports with example files to ensure 100% accuracy
- **Flexible Configuration**: YAML-based configuration for easy customization

## Supported Reports

The application supports the following POMS reports:

1. Customer Service Performance (Multiple Channel)
2. Customer Service Performance (Telephone)
3. Incident Management (Case Acknowledgement)
4. Incident Management (RIT)
5. Incident Management S4
6. Incident Management S123
7. Service Request
8. System Availability (Infra)
9. System Performance (Response Time)

## Installation

### Prerequisites

- Python 3.8 or higher
- MySQL database access
- Windows/Linux/macOS

### Setup Instructions

1. **Clone or download the application files**
   ```bash
   # If using git
   git clone <repository-url>
   cd poms_report_generator
   
   # Or extract the provided ZIP file
   ```

2. **Install Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure database connections**
   
   Edit the `config.yaml` file to match your database settings:
   ```yaml
   database:
     cdccrm:
       host: "your-database-host"
       port: 3306
       database: "cdccrm"
       username: "your-username"
       password: "your-password"
     cdc_poms:
       host: "your-database-host"
       port: 3306
       database: "cdc_poms"
       username: "your-username"
       password: "your-password"
   ```

4. **Test database connections**
   ```bash
   python database_manager.py
   ```

## Usage

### GUI Application

1. **Start the application**
   ```bash
   python gui_application.py
   ```

2. **Using the interface**
   - Set the **Year** and **Month** parameters
   - Select reports to generate (individual or "Select All")
   - Choose output directory
   - Click "Generate Reports"
   - Monitor progress in the progress area

3. **Test database connections**
   - Click "Test Database Connections" to verify connectivity

### Command Line Usage

You can also use the components programmatically:

```python
from independent_report_generator import IndependentReportGenerator

# Initialize generator
generator = IndependentReportGenerator()

# Generate a single report
result = generator.generate_single_report("Service Request", 2025, 1)

# Generate multiple reports
report_names = ["Service Request", "System Availability (Infra)"]
results = generator.generate_multiple_reports(report_names, 2025, 1)
```

## Configuration

### Main Configuration File (config.yaml)

The application uses a YAML configuration file with the following sections:

- **database**: Database connection settings
- **application**: Application-level settings (output directory, logging, etc.)
- **reports**: Report-specific configurations and file naming patterns

### Output File Naming

Generated files follow this pattern:
```
[Report Name]_[MMM][YYYY].xlsx
```

Examples:
- `Service Request_JAN2025.xlsx`
- `System Availability (Infra)_DEC2024.xlsx`

## Validation

The application includes a validation system to ensure generated reports match the expected format:

```bash
# Run validation tests
python validation_testing.py
```

The validator compares:
- File structure (rows, columns)
- Column names and data types
- Data content (with configurable tolerance for numerical values)

## Logging

The application creates detailed logs in:
- Console output (during execution)
- `poms_report_generator.log` file

Log levels can be configured in `config.yaml`:
```yaml
application:
  log_level: "INFO"  # DEBUG, INFO, WARNING, ERROR
```

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Verify database host, port, username, and password in `config.yaml`
   - Ensure database server is accessible from your network
   - Check firewall settings

2. **Missing Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Permission Errors**
   - Ensure write permissions to output directory
   - Run as administrator if necessary (Windows)

4. **Excel File Issues**
   - Close any open Excel files before generation
   - Ensure sufficient disk space

### Error Messages

- **"Report definition not found"**: Check that the report is defined in `report_definitions.py`
- **"Database configuration not found"**: Verify database name in `config.yaml`
- **"SQL query not found for report"**: Check that the corresponding `.sql` file exists in the `queries/` folder

## File Structure

```
poms_report_generator/
├── gui_application.py              # Main GUI application
├── independent_report_generator.py # Core report generation engine
├── report_definitions.py           # Report definitions and metadata
├── report_formatter.py             # Report-specific formatting classes
├── database_manager.py             # Database connection management
├── config_manager.py               # Configuration file handling
├── validation_testing.py           # Report validation system
├── config.yaml                     # Main configuration file
├── requirements.txt                # Python dependencies
├── README.md                       # This documentation
├── queries/                        # Independent SQL query files
├── example/                        # Example output files for validation
├── output/                         # Generated report files (created automatically)
└── logs/                           # Log files (created automatically)
```

## Development

### Adding New Reports

1. Create a SQL query file in the `queries/` folder (e.g., `new_report.sql`)
2. Add report definition to `report_definitions.py`:
   ```python
   reports['New Report Name'] = ReportDefinition(
       name='New Report Name',
       description='Description of the new report',
       database='cdc_poms',
       sql_file='new_report.sql',
       columns=[...],  # Define column specifications
       parameter_count=2,
       parameter_order=['year', 'month'],
       special_processing=[]
   )
   ```
3. Add formatting rules to `report_formatter.py` if needed
4. Place an example output file in the `example/` folder for validation

### Extending Functionality

The application is modular and can be extended:

- **Custom formatters**: Modify `format_dataframe_for_excel()` in `report_generator.py`
- **Additional databases**: Add new database configurations to `config.yaml`
- **Custom validation**: Extend the `ReportValidator` class
- **New output formats**: Modify the save methods in `report_generator.py`

## Support

For issues or questions:

1. Check the troubleshooting section above
2. Review log files for detailed error messages
3. Verify configuration settings
4. Test database connectivity

## License

This application is developed for internal use. Please ensure compliance with your organization's software policies.

## Version History

- **v2.0.0**: Modern SQL-based architecture with independent query files and enhanced formatting
- **v1.0.0**: Initial release with BIRT report conversion and GUI interface (deprecated)
