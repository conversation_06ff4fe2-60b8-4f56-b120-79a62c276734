#!/usr/bin/env python3
"""
Analyze all example Excel files to extract exact column headers and structure
"""

import pandas as pd
import os
from pathlib import Path
import json

def analyze_example_files():
    """Analyze all example Excel files and extract their structure"""
    
    example_dir = Path("example")
    if not example_dir.exists():
        print("Example directory not found!")
        return
    
    analysis = {}
    
    print("="*80)
    print("ANALYZING ALL EXAMPLE EXCEL FILES")
    print("="*80)
    
    for excel_file in example_dir.glob("*.xlsx"):
        print(f"\n📁 Analyzing: {excel_file.name}")
        print("-" * 60)
        
        try:
            # Read the Excel file
            df = pd.read_excel(excel_file)
            
            # Extract report name from filename (remove date suffix)
            report_name = excel_file.stem
            if '_' in report_name:
                report_name = report_name.rsplit('_', 1)[0]
            
            # Analyze structure
            file_analysis = {
                'filename': excel_file.name,
                'report_name': report_name,
                'shape': df.shape,
                'columns': list(df.columns),
                'column_count': len(df.columns),
                'row_count': len(df),
                'data_types': df.dtypes.astype(str).to_dict(),
                'sample_data': {}
            }
            
            # Get sample data for each column
            for col in df.columns:
                non_null_values = df[col].dropna()
                if len(non_null_values) > 0:
                    file_analysis['sample_data'][col] = {
                        'first_value': str(non_null_values.iloc[0]),
                        'last_value': str(non_null_values.iloc[-1]) if len(non_null_values) > 1 else str(non_null_values.iloc[0]),
                        'unique_count': len(non_null_values.unique()),
                        'data_type': str(non_null_values.dtype)
                    }
            
            # Check for special rows (MTD, totals, etc.)
            special_rows = []
            for idx, row in df.iterrows():
                first_col_value = str(row.iloc[0]).upper()
                if any(keyword in first_col_value for keyword in ['MTD', 'TOTAL', 'SUMMARY', 'NON COMPLIANCE', 'TRANSAKSI']):
                    special_rows.append({
                        'row_index': idx,
                        'first_column_value': str(row.iloc[0]),
                        'row_data': row.to_dict()
                    })
            
            file_analysis['special_rows'] = special_rows
            
            analysis[report_name] = file_analysis
            
            # Print analysis
            print(f"Shape: {df.shape}")
            print(f"Columns ({len(df.columns)}):")
            for i, col in enumerate(df.columns):
                print(f"  {i+1:2d}. '{col}'")
            
            if special_rows:
                print(f"Special rows found: {len(special_rows)}")
                for sr in special_rows:
                    print(f"  Row {sr['row_index']}: {sr['first_column_value']}")
            
            # Show first few rows
            print("First 3 rows:")
            print(df.head(3).to_string())
            
            # Show last few rows if there are special rows
            if special_rows:
                print("Last 3 rows:")
                print(df.tail(3).to_string())
                
        except Exception as e:
            print(f"❌ Error analyzing {excel_file.name}: {str(e)}")
            analysis[report_name] = {'error': str(e)}
    
    # Save analysis to JSON file
    with open('example_analysis.json', 'w') as f:
        json.dump(analysis, f, indent=2, default=str)
    
    print(f"\n{'='*80}")
    print("SUMMARY OF ALL REPORTS")
    print("="*80)
    
    for report_name, data in analysis.items():
        if 'error' in data:
            print(f"❌ {report_name}: {data['error']}")
        else:
            print(f"✅ {report_name}:")
            print(f"   Shape: {data['shape']}")
            print(f"   Columns: {data['column_count']}")
            print(f"   Special rows: {len(data['special_rows'])}")
    
    return analysis

if __name__ == "__main__":
    analyze_example_files()
