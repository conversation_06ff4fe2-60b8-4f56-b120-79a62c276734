#!/usr/bin/env python3
"""
Test System Performance report specifically
"""

from independent_report_generator import IndependentReportGenerator
import logging

logging.basicConfig(level=logging.INFO)

def main():
    generator = IndependentReportGenerator()
    result = generator.generate_single_report('System Performance (Response Time)', 2025, 1)
    print(f'Success: {result["success"]}')
    print(f'Rows: {result["rows_generated"]}')
    if not result['success']:
        print(f'Error: {result["error_message"]}')
    else:
        print(f'File: {result["filename"]}')

if __name__ == "__main__":
    main()
