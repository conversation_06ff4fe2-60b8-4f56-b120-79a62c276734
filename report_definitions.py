"""
Independent Report System - Report Definitions

This module defines all report configurations without dependency on BIRT files.
Each report has its metadata, column mappings, formatting rules, and processing requirements.

The refactored system now separates styling concerns from report definitions:
- Excel styling classes are in excel_styling.py
- This module focuses on report structure and data mapping
- Backward compatibility is maintained for existing usage
- New reference-based styling system supports configuration-driven styling
"""

from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
from pathlib import Path

# Import styling classes from the new excel_styling module
from excel_styling import (
    ExcelCellStyle, FontStyle, FillStyle, BorderStyle, CellBorders, AlignmentStyle,
    FontWeight, HorizontalAlignment, VerticalAlignment, BorderStyleType, FillType,
    StylingPresets, StylingUtils, ColumnStyleFactory, get_style_registry, get_style_configuration
)


@dataclass
class ColumnDefinition:
    """Definition for a report column with Excel styling support

    Supports both direct styling (backward compatibility) and reference-based styling (new system).
    The new system allows referencing styles by name from the style registry.
    """
    name: str
    data_type: str
    format_rule: Optional[str] = None
    width: Optional[int] = None
    alignment: str = 'left'  # Kept for backward compatibility

    # Direct Excel styling configuration (backward compatibility)
    header_style: Optional[ExcelCellStyle] = None
    data_style: Optional[ExcelCellStyle] = None

    # Reference-based styling configuration (new system)
    header_style_name: Optional[str] = None
    data_style_name: Optional[str] = None

    def __post_init__(self):
        """Initialize default styling if none provided"""
        # If using reference-based styling, resolve style names to actual styles
        if self.header_style_name and not self.header_style:
            self.header_style = get_style_registry().get_style(self.header_style_name)

        if self.data_style_name and not self.data_style:
            self.data_style = get_style_registry().get_style(self.data_style_name)

        # Convert legacy alignment to new alignment style for data cells
        if self.data_style is None:
            horizontal_align = HorizontalAlignment.LEFT
            if self.alignment == 'center':
                horizontal_align = HorizontalAlignment.CENTER
            elif self.alignment == 'right':
                horizontal_align = HorizontalAlignment.RIGHT

            self.data_style = ExcelCellStyle(
                alignment=AlignmentStyle(horizontal=horizontal_align)
            )

        # Set default header style if none provided
        if self.header_style is None:
            self.header_style = ExcelCellStyle(
                font=FontStyle(color="FFFFFFFF", weight=FontWeight.BOLD),
                fill=FillStyle(fill_type=FillType.SOLID, start_color="FF242424"),
                alignment=AlignmentStyle(horizontal=HorizontalAlignment.CENTER),
                borders=CellBorders.all_borders()
            )

    def get_header_style(self) -> ExcelCellStyle:
        """Get the header style for this column"""
        return self.header_style or ExcelCellStyle()

    def get_data_style(self) -> ExcelCellStyle:
        """Get the data style for this column"""
        return self.data_style or ExcelCellStyle()

    def set_number_format(self, format_string: str):
        """Set number format for data cells"""
        if self.data_style is None:
            self.data_style = ExcelCellStyle()
        self.data_style.number_format = format_string

    def set_data_alignment(self, horizontal: HorizontalAlignment, vertical: VerticalAlignment = VerticalAlignment.CENTER):
        """Set alignment for data cells"""
        if self.data_style is None:
            self.data_style = ExcelCellStyle()
        if self.data_style.alignment is None:
            self.data_style.alignment = AlignmentStyle()
        self.data_style.alignment.horizontal = horizontal
        self.data_style.alignment.vertical = vertical

    def set_data_font(self, name: str = None, size: int = None, color: str = None,
                      weight: FontWeight = FontWeight.NORMAL, italic: bool = False):
        """Set font styling for data cells"""
        if self.data_style is None:
            self.data_style = ExcelCellStyle()
        if self.data_style.font is None:
            self.data_style.font = FontStyle()

        if name is not None:
            self.data_style.font.name = name
        if size is not None:
            self.data_style.font.size = size
        if color is not None:
            self.data_style.font.color = color
        self.data_style.font.weight = weight
        self.data_style.font.italic = italic

    def set_data_fill(self, color: str, fill_type: FillType = FillType.SOLID):
        """Set background fill for data cells"""
        if self.data_style is None:
            self.data_style = ExcelCellStyle()
        self.data_style.fill = FillStyle(fill_type=fill_type, start_color=color)

    def set_data_borders(self, style: BorderStyleType = BorderStyleType.THIN, color: str = "FF000000"):
        """Set borders for data cells"""
        if self.data_style is None:
            self.data_style = ExcelCellStyle()
        self.data_style.borders = CellBorders.all_borders(style=style, color=color)


@dataclass
class ReportDefinition:
    """Complete definition for a report with styling support"""
    name: str
    description: str
    database: str
    sql_file: str
    columns: List[ColumnDefinition]
    parameter_count: int
    parameter_order: List[str]
    special_processing: List[str]
    sheet_name: str = 'Sheet0'

    # Default styling configuration
    default_header_style: Optional[ExcelCellStyle] = None
    default_data_style: Optional[ExcelCellStyle] = None

    def __post_init__(self):
        """Initialize default styling if none provided"""
        if self.default_header_style is None:
            self.default_header_style = StylingPresets.get_header_style()

        if self.default_data_style is None:
            self.default_data_style = StylingPresets.get_text_data_style()

    def get_column_header_style(self, column_name: str) -> ExcelCellStyle:
        """Get header style for a specific column"""
        for col in self.columns:
            if col.name == column_name:
                return col.get_header_style()
        return self.default_header_style

    def get_column_data_style(self, column_name: str) -> ExcelCellStyle:
        """Get data style for a specific column"""
        for col in self.columns:
            if col.name == column_name:
                return col.get_data_style()
        return self.default_data_style

    def apply_default_styling_to_columns(self):
        """Apply default styling to columns that don't have specific styling"""
        for col in self.columns:
            if col.header_style is None:
                col.header_style = self.default_header_style
            if col.data_style is None:
                col.data_style = self.default_data_style

    def set_report_wide_styling(self, header_style: ExcelCellStyle = None, data_style: ExcelCellStyle = None):
        """Set styling for all columns in the report"""
        if header_style:
            self.default_header_style = header_style
            for col in self.columns:
                col.header_style = header_style

        if data_style:
            self.default_data_style = data_style
            for col in self.columns:
                col.data_style = data_style


class ReportRegistry:
    """Registry of all available reports"""
    
    def __init__(self):
        self.reports = self._initialize_reports()
    
    def _initialize_reports(self) -> Dict[str, ReportDefinition]:
        """Initialize all report definitions"""
        
        reports = {}
        
        # Customer Service Performance (Multiple Channel)
        reports['Customer Service Performance (Multiple Channel)'] = ReportDefinition(
            name='Customer Service Performance (Multiple Channel)',
            description='Customer service performance across multiple channels',
            database='cdc_poms',
            sql_file='customer_service_multiple_channel.sql',
            columns=[
                ColumnDefinition('Case Number', 'string', width=15),
                ColumnDefinition('Channel', 'string', width=15),
                ColumnDefinition('Actual Start DateTime', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=20),
                ColumnDefinition('Completed DateTime', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=20),
                ColumnDefinition('Available SLA (Second)', 'integer', width=20),
                ColumnDefinition('Actual SLA (Second)', 'integer', width=18)
            ],
            parameter_count=2,
            parameter_order=['year', 'month'],
            special_processing=[]
        )
        
        # Customer Service Performance (Telephone)
        reports['Customer Service Performance (Telephone)'] = ReportDefinition(
            name='Customer Service Performance (Telephone)',
            description='Customer service performance for telephone channel with MTD summary',
            database='cdc_poms',
            sql_file='customer_service_telephone.sql',
            columns=[
                ColumnStyleFactory.create_datetime_column('DATE CALL', width=18),
                ColumnStyleFactory.create_number_column('CALL OFFER', width=12),
                ColumnStyleFactory.create_number_column('CALL HANDLE', width=12),
                ColumnStyleFactory.create_number_column('CALL WITHIN 10', width=15),
                ColumnStyleFactory.create_number_column('ABANDON SHORT', width=15),
                ColumnStyleFactory.create_number_column('ABANDON LONG', width=15),
                ColumnStyleFactory.create_percentage_column('ABANDON %', width=12),
                ColumnStyleFactory.create_percentage_column('ANSWER %', width=12),
                ColumnStyleFactory.create_percentage_column('SLA %', width=12)
            ],
            parameter_count=2,
            parameter_order=['year', 'month'],
            special_processing=['add_mtd_row', 'rename_service_level_column']
        )
        
        # Incident Management (Case Acknowledgement)
        reports['Incident Management (Case Acknowledgement)'] = ReportDefinition(
            name='Incident Management (Case Acknowledgement)',
            description='Incident management case acknowledgement tracking',
            database='cdc_poms',
            sql_file='incident_management_case_acknowledgement.sql',
            columns=[
                ColumnDefinition('Case Number', 'string', width=15),
                ColumnDefinition('Case Name', 'string', width=50),
                ColumnDefinition('Task Number', 'string', width=15),
                ColumnDefinition('SLA Flag', 'string', width=10),
                ColumnDefinition('Actual Start DateTime', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=20),
                ColumnDefinition('Completed DateTime', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=20),
                ColumnDefinition('Available SLA (Second)', 'integer', width=20),
                ColumnDefinition('Actual SLA (Second)', 'integer', width=18)
            ],
            parameter_count=4,
            parameter_order=['year', 'month', 'year', 'month'],
            special_processing=[]
        )
        
        # Incident Management (RIT)
        reports['Incident Management (RIT)'] = ReportDefinition(
            name='Incident Management (RIT)',
            description='Incident management RIT tracking',
            database='cdc_poms',
            sql_file='incident_management_rit.sql',
            columns=[
                ColumnDefinition('Case Number', 'string', width=15),
                ColumnDefinition('Case Name', 'string', width=50),
                ColumnDefinition('Task Number', 'string', width=15),
                ColumnDefinition('SLA Flag', 'string', width=10),
                ColumnDefinition('Actual Start DateTime', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=20),
                ColumnDefinition('Completed DateTime', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=20),
                ColumnDefinition('Available SLA (Second)', 'integer', width=20),
                ColumnDefinition('Actual SLA (Second)', 'integer', width=18)
            ],
            parameter_count=4,
            parameter_order=['year', 'month', 'year', 'month'],
            special_processing=[]
        )
        
        # Incident Management S123
        reports['Incident Management S123'] = ReportDefinition(
            name='Incident Management S123',
            description='Incident management S123 severity tracking',
            database='cdc_poms',
            sql_file='incident_management_s123.sql',
            columns=[
                ColumnDefinition('Case Number', 'string', width=15),
                ColumnDefinition('Case Name', 'string', width=50),
                ColumnDefinition('Task Number', 'string', width=15),
                ColumnDefinition('SLA Flag', 'string', width=10),
                ColumnDefinition('Actual Start DateTime', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=20),
                ColumnDefinition('Completed DateTime', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=20),
                ColumnDefinition('Available SLA (Day)', 'integer', width=18),
                ColumnDefinition('Actual SLA (Day)', 'integer', width=16)
            ],
            parameter_count=6,
            parameter_order=['year', 'month', 'year', 'month', 'year', 'month'],
            special_processing=[]
        )
        
        # Incident Management S4
        reports['Incident Management S4'] = ReportDefinition(
            name='Incident Management S4',
            description='Incident management S4 severity tracking',
            database='cdc_poms',
            sql_file='incident_management_s4.sql',
            columns=[
                ColumnDefinition('Case Number', 'string', width=15),
                ColumnDefinition('Redmine No.', 'string', width=12),
                ColumnDefinition('Case Name        ', 'string', width=50),  # Note: exact spacing from example
                ColumnDefinition('Task Name', 'string', width=50),
                ColumnDefinition('SLA Flag', 'string', width=10),
                ColumnDefinition('Actual Start DateTime', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=20),
                ColumnDefinition('Completed DateTime', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=20),
                ColumnDefinition('Duration', 'string', width=20),
                ColumnDefinition('Available SLA (Day)', 'integer', width=18),
                ColumnDefinition('Actual SLA (Day)', 'integer', width=16),
                ColumnDefinition('Implementation Issue?', 'string', width=20),
                ColumnDefinition('Exceed SLA (Day)', 'integer', width=16)
            ],
            parameter_count=5,
            parameter_order=['year', 'month', 'year', 'month', 'year'],
            special_processing=[]
        )
        
        # Service Request
        reports['Service Request'] = ReportDefinition(
            name='Service Request',
            description='Service request tracking',
            database='cdc_poms',
            sql_file='service_request.sql',
            columns=[
                ColumnDefinition('TICKET NUMBER', 'string', width=15),
                ColumnDefinition('CATEGORY', 'string', width=20),
                ColumnDefinition('CREATED DATE', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=20),
                ColumnDefinition('REQUEST SUBJECT', 'string', width=50),
                ColumnDefinition('REASON FOR REQUEST', 'string', width=25),
                ColumnDefinition('EXPECTED START', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=18),
                ColumnDefinition('EXPECTED COMPLETED', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=20),
                ColumnDefinition('EXPECTED DURATION', 'integer', width=18),
                ColumnDefinition('ACTUAL START', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=18),
                ColumnDefinition('ACTUAL COMPLETED', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=18),
                ColumnDefinition('ACTUAL DURATION', 'integer', width=16),
                ColumnDefinition('EXCEED DURATION', 'integer', width=16)
            ],
            parameter_count=2,
            parameter_order=['year', 'month'],
            special_processing=[]
        )
        
        # System Availability (Infra)
        reports['System Availability (Infra)'] = ReportDefinition(
            name='System Availability (Infra)',
            description='System availability infrastructure monitoring',
            database='cdc_poms',
            sql_file='system_availability.sql',
            columns=[
                ColumnDefinition('HOST GROUP', 'string', width=15),
                ColumnDefinition('DOWNTIME', 'string', width=20),
                ColumnDefinition('COUNTER', 'integer', width=12),
                ColumnDefinition('ALL DOWN FLAG', 'string', width=15)
            ],
            parameter_count=2,
            parameter_order=['month', 'year'],
            special_processing=[]
        )
        
        # System Performance (Response Time)
        system_perf_col1 = ColumnDefinition(
            'System Performance', 'string', width=45,
            header_style=StylingPresets.get_special_header_style(),
            data_style=StylingPresets.get_text_data_style()
        )
        system_perf_col2 = ColumnDefinition(
            'Januari 2025', 'string', width=12,  # This will be dynamic based on month/year
            header_style=StylingPresets.get_highlight_style(),
            data_style=StylingPresets.get_number_data_style()
        )
        system_perf_col3 = ColumnDefinition(
            'Unnamed: 2', 'string', width=12,
            header_style=StylingPresets.get_highlight_style(),
            data_style=StylingPresets.get_number_data_style()
        )
        system_perf_col4 = ColumnDefinition(
            'Unnamed: 3', 'string', width=12,
            header_style=StylingPresets.get_highlight_style(),
            data_style=StylingPresets.get_number_data_style()
        )
        system_perf_col5 = ColumnDefinition(
            'Unnamed: 4', 'string', width=12,
            header_style=StylingPresets.get_highlight_style(),
            data_style=StylingPresets.get_percentage_data_style()
        )

        reports['System Performance (Response Time)'] = ReportDefinition(
            name='System Performance (Response Time)',
            description='System performance response time monitoring with 16 transactions',
            database='cdc_poms',
            sql_file='system_performance.sql',
            columns=[system_perf_col1, system_perf_col2, system_perf_col3, system_perf_col4, system_perf_col5],
            parameter_count=30,  # 15 UNION blocks * 2 parameters each
            parameter_order=['month', 'year'] * 15,
            special_processing=['add_header_row', 'add_non_compliance_summary']
        )
        
        return reports
    
    def get_report(self, name: str) -> Optional[ReportDefinition]:
        """Get a report definition by name"""
        return self.reports.get(name)
    
    def get_all_reports(self) -> Dict[str, ReportDefinition]:
        """Get all report definitions"""
        return self.reports
    
    def get_report_names(self) -> List[str]:
        """Get list of all report names"""
        return list(self.reports.keys())
    
    def load_sql_query(self, sql_file: str) -> str:
        """Load SQL query from file"""
        sql_path = Path('queries') / sql_file
        if sql_path.exists():
            with open(sql_path, 'r', encoding='utf-8') as f:
                content = f.read()
                # Extract just the SQL query (remove comments)
                lines = content.split('\n')
                sql_lines = []
                for line in lines:
                    if not line.strip().startswith('--') and line.strip():
                        sql_lines.append(line)
                return '\n'.join(sql_lines).strip().rstrip(';')
        return ""


# Global registry instance
REPORT_REGISTRY = ReportRegistry()
