"""
Independent Report System - Report Definitions

This module defines all report configurations without dependency on BIRT files.
Each report has its metadata, column mappings, formatting rules, and processing requirements.
"""

from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
from pathlib import Path
from enum import Enum


# Excel Styling Enums
class FontWeight(Enum):
    """Font weight options"""
    NORMAL = "normal"
    BOLD = "bold"


class HorizontalAlignment(Enum):
    """Horizontal alignment options"""
    LEFT = "left"
    CENTER = "center"
    RIGHT = "right"
    JUSTIFY = "justify"


class VerticalAlignment(Enum):
    """Vertical alignment options"""
    TOP = "top"
    CENTER = "center"
    BOTTOM = "bottom"


class BorderStyleType(Enum):
    """Border style options"""
    NONE = "none"
    THIN = "thin"
    MEDIUM = "medium"
    THICK = "thick"


class FillType(Enum):
    """Fill pattern types"""
    NONE = "none"
    SOLID = "solid"


# Excel Styling Data Classes
@dataclass
class FontStyle:
    """Font styling configuration"""
    name: Optional[str] = None
    size: Optional[int] = None
    color: Optional[str] = None  # Hex color code (e.g., "FF000000")
    weight: FontWeight = FontWeight.NORMAL
    italic: bool = False
    underline: bool = False


@dataclass
class FillStyle:
    """Fill/background styling configuration"""
    fill_type: FillType = FillType.NONE
    start_color: Optional[str] = None  # Hex color code
    end_color: Optional[str] = None    # Hex color code (for gradients)


@dataclass
class BorderStyle:
    """Border styling configuration"""
    style: BorderStyleType = BorderStyleType.NONE
    color: Optional[str] = None  # Hex color code


@dataclass
class CellBorders:
    """Complete border configuration for a cell"""
    left: Optional[BorderStyle] = None
    right: Optional[BorderStyle] = None
    top: Optional[BorderStyle] = None
    bottom: Optional[BorderStyle] = None

    @classmethod
    def all_borders(cls, style: BorderStyleType = BorderStyleType.THIN, color: str = "FF000000"):
        """Create borders for all sides with same style and color"""
        border_style = BorderStyle(style=style, color=color)
        return cls(left=border_style, right=border_style, top=border_style, bottom=border_style)


@dataclass
class AlignmentStyle:
    """Alignment styling configuration"""
    horizontal: HorizontalAlignment = HorizontalAlignment.LEFT
    vertical: VerticalAlignment = VerticalAlignment.CENTER
    wrap_text: bool = False


@dataclass
class ExcelCellStyle:
    """Complete Excel cell styling configuration"""
    font: Optional[FontStyle] = None
    fill: Optional[FillStyle] = None
    borders: Optional[CellBorders] = None
    alignment: Optional[AlignmentStyle] = None
    number_format: Optional[str] = None  # Excel number format string


@dataclass
class ColumnDefinition:
    """Definition for a report column with Excel styling support"""
    name: str
    data_type: str
    format_rule: Optional[str] = None
    width: Optional[int] = None
    alignment: str = 'left'  # Kept for backward compatibility

    # Excel styling configuration
    header_style: Optional[ExcelCellStyle] = None
    data_style: Optional[ExcelCellStyle] = None

    def __post_init__(self):
        """Initialize default styling if none provided"""
        # Convert legacy alignment to new alignment style for data cells
        if self.data_style is None:
            horizontal_align = HorizontalAlignment.LEFT
            if self.alignment == 'center':
                horizontal_align = HorizontalAlignment.CENTER
            elif self.alignment == 'right':
                horizontal_align = HorizontalAlignment.RIGHT

            self.data_style = ExcelCellStyle(
                alignment=AlignmentStyle(horizontal=horizontal_align)
            )

        # Set default header style if none provided
        if self.header_style is None:
            self.header_style = ExcelCellStyle(
                font=FontStyle(color="FFFFFFFF", weight=FontWeight.BOLD),
                fill=FillStyle(fill_type=FillType.SOLID, start_color="FF242424"),
                alignment=AlignmentStyle(horizontal=HorizontalAlignment.CENTER),
                borders=CellBorders.all_borders()
            )

    def get_header_style(self) -> ExcelCellStyle:
        """Get the header style for this column"""
        return self.header_style or ExcelCellStyle()

    def get_data_style(self) -> ExcelCellStyle:
        """Get the data style for this column"""
        return self.data_style or ExcelCellStyle()

    def set_number_format(self, format_string: str):
        """Set number format for data cells"""
        if self.data_style is None:
            self.data_style = ExcelCellStyle()
        self.data_style.number_format = format_string

    def set_data_alignment(self, horizontal: HorizontalAlignment, vertical: VerticalAlignment = VerticalAlignment.CENTER):
        """Set alignment for data cells"""
        if self.data_style is None:
            self.data_style = ExcelCellStyle()
        if self.data_style.alignment is None:
            self.data_style.alignment = AlignmentStyle()
        self.data_style.alignment.horizontal = horizontal
        self.data_style.alignment.vertical = vertical

    def set_data_font(self, name: str = None, size: int = None, color: str = None,
                      weight: FontWeight = FontWeight.NORMAL, italic: bool = False):
        """Set font styling for data cells"""
        if self.data_style is None:
            self.data_style = ExcelCellStyle()
        if self.data_style.font is None:
            self.data_style.font = FontStyle()

        if name is not None:
            self.data_style.font.name = name
        if size is not None:
            self.data_style.font.size = size
        if color is not None:
            self.data_style.font.color = color
        self.data_style.font.weight = weight
        self.data_style.font.italic = italic

    def set_data_fill(self, color: str, fill_type: FillType = FillType.SOLID):
        """Set background fill for data cells"""
        if self.data_style is None:
            self.data_style = ExcelCellStyle()
        self.data_style.fill = FillStyle(fill_type=fill_type, start_color=color)

    def set_data_borders(self, style: BorderStyleType = BorderStyleType.THIN, color: str = "FF000000"):
        """Set borders for data cells"""
        if self.data_style is None:
            self.data_style = ExcelCellStyle()
        self.data_style.borders = CellBorders.all_borders(style=style, color=color)


class StylingPresets:
    """Predefined styling presets for different data types and report elements"""

    @staticmethod
    def get_header_style(background_color: str = "FF242424", font_color: str = "FFFFFFFF") -> ExcelCellStyle:
        """Standard header style"""
        return ExcelCellStyle(
            font=FontStyle(color=font_color, weight=FontWeight.BOLD),
            fill=FillStyle(fill_type=FillType.SOLID, start_color=background_color),
            alignment=AlignmentStyle(horizontal=HorizontalAlignment.CENTER, vertical=VerticalAlignment.CENTER),
            borders=CellBorders.all_borders()
        )

    @staticmethod
    def get_text_data_style() -> ExcelCellStyle:
        """Standard text data style"""
        return ExcelCellStyle(
            alignment=AlignmentStyle(horizontal=HorizontalAlignment.LEFT, vertical=VerticalAlignment.CENTER),
            borders=CellBorders.all_borders()
        )

    @staticmethod
    def get_number_data_style(decimal_places: int = 0) -> ExcelCellStyle:
        """Standard number data style"""
        number_format = "0" if decimal_places == 0 else f"0.{'0' * decimal_places}"
        return ExcelCellStyle(
            alignment=AlignmentStyle(horizontal=HorizontalAlignment.RIGHT, vertical=VerticalAlignment.CENTER),
            borders=CellBorders.all_borders(),
            number_format=number_format
        )

    @staticmethod
    def get_percentage_data_style(decimal_places: int = 2) -> ExcelCellStyle:
        """Standard percentage data style"""
        number_format = f"0.{'0' * decimal_places}"
        return ExcelCellStyle(
            alignment=AlignmentStyle(horizontal=HorizontalAlignment.RIGHT, vertical=VerticalAlignment.CENTER),
            borders=CellBorders.all_borders(),
            number_format=number_format
        )

    @staticmethod
    def get_date_data_style(format_string: str = "yyyy-mm-dd") -> ExcelCellStyle:
        """Standard date data style"""
        return ExcelCellStyle(
            alignment=AlignmentStyle(horizontal=HorizontalAlignment.CENTER, vertical=VerticalAlignment.CENTER),
            borders=CellBorders.all_borders(),
            number_format=format_string
        )

    @staticmethod
    def get_datetime_data_style(format_string: str = "yyyy-mm-dd hh:mm:ss") -> ExcelCellStyle:
        """Standard datetime data style"""
        return ExcelCellStyle(
            alignment=AlignmentStyle(horizontal=HorizontalAlignment.CENTER, vertical=VerticalAlignment.CENTER),
            borders=CellBorders.all_borders(),
            number_format=format_string
        )

    @staticmethod
    def get_summary_row_style(background_color: str = "FFE2E2E2") -> ExcelCellStyle:
        """Standard summary row style"""
        return ExcelCellStyle(
            font=FontStyle(weight=FontWeight.BOLD),
            fill=FillStyle(fill_type=FillType.SOLID, start_color=background_color),
            alignment=AlignmentStyle(horizontal=HorizontalAlignment.LEFT, vertical=VerticalAlignment.CENTER),
            borders=CellBorders.all_borders()
        )

    @staticmethod
    def get_special_header_style(background_color: str = "FF2C2C2C", font_color: str = "FFFFFFFF") -> ExcelCellStyle:
        """Special header style for System Performance reports"""
        return ExcelCellStyle(
            font=FontStyle(color=font_color, weight=FontWeight.BOLD),
            fill=FillStyle(fill_type=FillType.SOLID, start_color=background_color),
            alignment=AlignmentStyle(horizontal=HorizontalAlignment.LEFT, vertical=VerticalAlignment.CENTER),
            borders=CellBorders.all_borders()
        )

    @staticmethod
    def get_highlight_style(background_color: str = "FFB8ED83", font_color: str = "FF004000") -> ExcelCellStyle:
        """Highlight style for special cells"""
        return ExcelCellStyle(
            font=FontStyle(color=font_color, weight=FontWeight.BOLD),
            fill=FillStyle(fill_type=FillType.SOLID, start_color=background_color),
            alignment=AlignmentStyle(horizontal=HorizontalAlignment.CENTER, vertical=VerticalAlignment.CENTER),
            borders=CellBorders.all_borders()
        )


class ColumnStyleFactory:
    """Factory class for creating styled column definitions"""

    @staticmethod
    def create_text_column(name: str, width: int = None) -> ColumnDefinition:
        """Create a text column with standard styling"""
        return ColumnDefinition(
            name=name,
            data_type='string',
            width=width,
            header_style=StylingPresets.get_header_style(),
            data_style=StylingPresets.get_text_data_style()
        )

    @staticmethod
    def create_number_column(name: str, width: int = None, decimal_places: int = 0) -> ColumnDefinition:
        """Create a number column with standard styling"""
        return ColumnDefinition(
            name=name,
            data_type='integer' if decimal_places == 0 else 'float',
            width=width,
            header_style=StylingPresets.get_header_style(),
            data_style=StylingPresets.get_number_data_style(decimal_places)
        )

    @staticmethod
    def create_percentage_column(name: str, width: int = None, decimal_places: int = 2) -> ColumnDefinition:
        """Create a percentage column with standard styling"""
        format_rule = f"0.{'0' * decimal_places}"
        return ColumnDefinition(
            name=name,
            data_type='float',
            format_rule=format_rule,
            width=width,
            header_style=StylingPresets.get_header_style(),
            data_style=StylingPresets.get_percentage_data_style(decimal_places)
        )

    @staticmethod
    def create_date_column(name: str, width: int = None, format_string: str = "yyyy-mm-dd") -> ColumnDefinition:
        """Create a date column with standard styling"""
        return ColumnDefinition(
            name=name,
            data_type='date',
            format_rule=format_string,
            width=width,
            header_style=StylingPresets.get_header_style(),
            data_style=StylingPresets.get_date_data_style(format_string)
        )

    @staticmethod
    def create_datetime_column(name: str, width: int = None, format_string: str = "yyyy-mm-dd hh:mm:ss") -> ColumnDefinition:
        """Create a datetime column with standard styling"""
        return ColumnDefinition(
            name=name,
            data_type='datetime',
            format_rule=format_string,
            width=width,
            header_style=StylingPresets.get_header_style(),
            data_style=StylingPresets.get_datetime_data_style(format_string)
        )


@dataclass
class ReportDefinition:
    """Complete definition for a report with styling support"""
    name: str
    description: str
    database: str
    sql_file: str
    columns: List[ColumnDefinition]
    parameter_count: int
    parameter_order: List[str]
    special_processing: List[str]
    sheet_name: str = 'Sheet0'

    # Default styling configuration
    default_header_style: Optional[ExcelCellStyle] = None
    default_data_style: Optional[ExcelCellStyle] = None

    def __post_init__(self):
        """Initialize default styling if none provided"""
        if self.default_header_style is None:
            self.default_header_style = StylingPresets.get_header_style()

        if self.default_data_style is None:
            self.default_data_style = StylingPresets.get_text_data_style()

    def get_column_header_style(self, column_name: str) -> ExcelCellStyle:
        """Get header style for a specific column"""
        for col in self.columns:
            if col.name == column_name:
                return col.get_header_style()
        return self.default_header_style

    def get_column_data_style(self, column_name: str) -> ExcelCellStyle:
        """Get data style for a specific column"""
        for col in self.columns:
            if col.name == column_name:
                return col.get_data_style()
        return self.default_data_style

    def apply_default_styling_to_columns(self):
        """Apply default styling to columns that don't have specific styling"""
        for col in self.columns:
            if col.header_style is None:
                col.header_style = self.default_header_style
            if col.data_style is None:
                col.data_style = self.default_data_style

    def set_report_wide_styling(self, header_style: ExcelCellStyle = None, data_style: ExcelCellStyle = None):
        """Set styling for all columns in the report"""
        if header_style:
            self.default_header_style = header_style
            for col in self.columns:
                col.header_style = header_style

        if data_style:
            self.default_data_style = data_style
            for col in self.columns:
                col.data_style = data_style


class StylingUtils:
    """Utility functions for Excel styling operations"""

    @staticmethod
    def validate_hex_color(color: str) -> bool:
        """Validate hex color format (e.g., 'FF000000')"""
        if not color or len(color) != 8:
            return False
        try:
            int(color, 16)
            return True
        except ValueError:
            return False

    @staticmethod
    def rgb_to_hex(r: int, g: int, b: int, alpha: int = 255) -> str:
        """Convert RGB values to hex color string"""
        return f"{alpha:02X}{r:02X}{g:02X}{b:02X}"

    @staticmethod
    def hex_to_rgb(hex_color: str) -> tuple:
        """Convert hex color string to RGB tuple"""
        if not StylingUtils.validate_hex_color(hex_color):
            return (0, 0, 0, 255)

        alpha = int(hex_color[0:2], 16)
        r = int(hex_color[2:4], 16)
        g = int(hex_color[4:6], 16)
        b = int(hex_color[6:8], 16)
        return (r, g, b, alpha)

    @staticmethod
    def merge_styles(base_style: ExcelCellStyle, override_style: ExcelCellStyle) -> ExcelCellStyle:
        """Merge two styles, with override_style taking precedence"""
        merged = ExcelCellStyle()

        # Merge font
        if base_style.font or override_style.font:
            merged.font = FontStyle()
            if base_style.font:
                merged.font.name = base_style.font.name
                merged.font.size = base_style.font.size
                merged.font.color = base_style.font.color
                merged.font.weight = base_style.font.weight
                merged.font.italic = base_style.font.italic
                merged.font.underline = base_style.font.underline

            if override_style.font:
                if override_style.font.name is not None:
                    merged.font.name = override_style.font.name
                if override_style.font.size is not None:
                    merged.font.size = override_style.font.size
                if override_style.font.color is not None:
                    merged.font.color = override_style.font.color
                merged.font.weight = override_style.font.weight
                merged.font.italic = override_style.font.italic
                merged.font.underline = override_style.font.underline

        # Merge fill
        merged.fill = override_style.fill or base_style.fill

        # Merge borders
        merged.borders = override_style.borders or base_style.borders

        # Merge alignment
        if base_style.alignment or override_style.alignment:
            merged.alignment = AlignmentStyle()
            if base_style.alignment:
                merged.alignment.horizontal = base_style.alignment.horizontal
                merged.alignment.vertical = base_style.alignment.vertical
                merged.alignment.wrap_text = base_style.alignment.wrap_text

            if override_style.alignment:
                merged.alignment.horizontal = override_style.alignment.horizontal
                merged.alignment.vertical = override_style.alignment.vertical
                merged.alignment.wrap_text = override_style.alignment.wrap_text

        # Merge number format
        merged.number_format = override_style.number_format or base_style.number_format

        return merged

    @staticmethod
    def create_conditional_style(condition: str, true_style: ExcelCellStyle, false_style: ExcelCellStyle = None) -> dict:
        """Create a conditional styling configuration"""
        return {
            'condition': condition,
            'true_style': true_style,
            'false_style': false_style or ExcelCellStyle()
        }

    @staticmethod
    def get_data_type_style(data_type: str, format_rule: str = None) -> ExcelCellStyle:
        """Get appropriate style based on data type"""
        if data_type == 'string':
            return StylingPresets.get_text_data_style()
        elif data_type == 'integer':
            return StylingPresets.get_number_data_style(0)
        elif data_type == 'float':
            if format_rule == '0.00':
                return StylingPresets.get_percentage_data_style(2)
            else:
                return StylingPresets.get_number_data_style(2)
        elif data_type == 'datetime':
            format_str = format_rule or "yyyy-mm-dd hh:mm:ss"
            return StylingPresets.get_datetime_data_style(format_str)
        elif data_type == 'date':
            format_str = format_rule or "yyyy-mm-dd"
            return StylingPresets.get_date_data_style(format_str)
        else:
            return StylingPresets.get_text_data_style()

    @staticmethod
    def apply_openpyxl_style(cell, style: ExcelCellStyle):
        """Apply ExcelCellStyle to an openpyxl cell"""
        from openpyxl.styles import Font, PatternFill, Alignment, Border, Side

        # Apply font
        if style.font:
            font_kwargs = {}
            if style.font.name:
                font_kwargs['name'] = style.font.name
            if style.font.size:
                font_kwargs['size'] = style.font.size
            if style.font.color:
                font_kwargs['color'] = style.font.color
            font_kwargs['bold'] = (style.font.weight == FontWeight.BOLD)
            font_kwargs['italic'] = style.font.italic
            font_kwargs['underline'] = 'single' if style.font.underline else 'none'

            cell.font = Font(**font_kwargs)

        # Apply fill
        if style.fill and style.fill.fill_type != FillType.NONE:
            fill_kwargs = {'fill_type': style.fill.fill_type.value}
            if style.fill.start_color:
                fill_kwargs['start_color'] = style.fill.start_color
            if style.fill.end_color:
                fill_kwargs['end_color'] = style.fill.end_color

            cell.fill = PatternFill(**fill_kwargs)

        # Apply alignment
        if style.alignment:
            alignment_kwargs = {
                'horizontal': style.alignment.horizontal.value,
                'vertical': style.alignment.vertical.value,
                'wrap_text': style.alignment.wrap_text
            }
            cell.alignment = Alignment(**alignment_kwargs)

        # Apply borders
        if style.borders:
            border_kwargs = {}
            if style.borders.left:
                border_kwargs['left'] = Side(style=style.borders.left.style.value, color=style.borders.left.color)
            if style.borders.right:
                border_kwargs['right'] = Side(style=style.borders.right.style.value, color=style.borders.right.color)
            if style.borders.top:
                border_kwargs['top'] = Side(style=style.borders.top.style.value, color=style.borders.top.color)
            if style.borders.bottom:
                border_kwargs['bottom'] = Side(style=style.borders.bottom.style.value, color=style.borders.bottom.color)

            cell.border = Border(**border_kwargs)

        # Apply number format
        if style.number_format:
            cell.number_format = style.number_format


class ReportRegistry:
    """Registry of all available reports"""
    
    def __init__(self):
        self.reports = self._initialize_reports()
    
    def _initialize_reports(self) -> Dict[str, ReportDefinition]:
        """Initialize all report definitions"""
        
        reports = {}
        
        # Customer Service Performance (Multiple Channel)
        reports['Customer Service Performance (Multiple Channel)'] = ReportDefinition(
            name='Customer Service Performance (Multiple Channel)',
            description='Customer service performance across multiple channels',
            database='cdc_poms',
            sql_file='customer_service_multiple_channel.sql',
            columns=[
                ColumnDefinition('Case Number', 'string', width=15),
                ColumnDefinition('Channel', 'string', width=15),
                ColumnDefinition('Actual Start DateTime', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=20),
                ColumnDefinition('Completed DateTime', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=20),
                ColumnDefinition('Available SLA (Second)', 'integer', width=20),
                ColumnDefinition('Actual SLA (Second)', 'integer', width=18)
            ],
            parameter_count=2,
            parameter_order=['year', 'month'],
            special_processing=[]
        )
        
        # Customer Service Performance (Telephone)
        reports['Customer Service Performance (Telephone)'] = ReportDefinition(
            name='Customer Service Performance (Telephone)',
            description='Customer service performance for telephone channel with MTD summary',
            database='cdc_poms',
            sql_file='customer_service_telephone.sql',
            columns=[
                ColumnStyleFactory.create_datetime_column('DATE CALL', width=18),
                ColumnStyleFactory.create_number_column('CALL OFFER', width=12),
                ColumnStyleFactory.create_number_column('CALL HANDLE', width=12),
                ColumnStyleFactory.create_number_column('CALL WITHIN 10', width=15),
                ColumnStyleFactory.create_number_column('ABANDON SHORT', width=15),
                ColumnStyleFactory.create_number_column('ABANDON LONG', width=15),
                ColumnStyleFactory.create_percentage_column('ABANDON %', width=12),
                ColumnStyleFactory.create_percentage_column('ANSWER %', width=12),
                ColumnStyleFactory.create_percentage_column('SLA %', width=12)
            ],
            parameter_count=2,
            parameter_order=['year', 'month'],
            special_processing=['add_mtd_row', 'rename_service_level_column']
        )
        
        # Incident Management (Case Acknowledgement)
        reports['Incident Management (Case Acknowledgement)'] = ReportDefinition(
            name='Incident Management (Case Acknowledgement)',
            description='Incident management case acknowledgement tracking',
            database='cdc_poms',
            sql_file='incident_management_case_acknowledgement.sql',
            columns=[
                ColumnDefinition('Case Number', 'string', width=15),
                ColumnDefinition('Case Name', 'string', width=50),
                ColumnDefinition('Task Number', 'string', width=15),
                ColumnDefinition('SLA Flag', 'string', width=10),
                ColumnDefinition('Actual Start DateTime', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=20),
                ColumnDefinition('Completed DateTime', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=20),
                ColumnDefinition('Available SLA (Second)', 'integer', width=20),
                ColumnDefinition('Actual SLA (Second)', 'integer', width=18)
            ],
            parameter_count=4,
            parameter_order=['year', 'month', 'year', 'month'],
            special_processing=[]
        )
        
        # Incident Management (RIT)
        reports['Incident Management (RIT)'] = ReportDefinition(
            name='Incident Management (RIT)',
            description='Incident management RIT tracking',
            database='cdc_poms',
            sql_file='incident_management_rit.sql',
            columns=[
                ColumnDefinition('Case Number', 'string', width=15),
                ColumnDefinition('Case Name', 'string', width=50),
                ColumnDefinition('Task Number', 'string', width=15),
                ColumnDefinition('SLA Flag', 'string', width=10),
                ColumnDefinition('Actual Start DateTime', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=20),
                ColumnDefinition('Completed DateTime', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=20),
                ColumnDefinition('Available SLA (Second)', 'integer', width=20),
                ColumnDefinition('Actual SLA (Second)', 'integer', width=18)
            ],
            parameter_count=4,
            parameter_order=['year', 'month', 'year', 'month'],
            special_processing=[]
        )
        
        # Incident Management S123
        reports['Incident Management S123'] = ReportDefinition(
            name='Incident Management S123',
            description='Incident management S123 severity tracking',
            database='cdc_poms',
            sql_file='incident_management_s123.sql',
            columns=[
                ColumnDefinition('Case Number', 'string', width=15),
                ColumnDefinition('Case Name', 'string', width=50),
                ColumnDefinition('Task Number', 'string', width=15),
                ColumnDefinition('SLA Flag', 'string', width=10),
                ColumnDefinition('Actual Start DateTime', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=20),
                ColumnDefinition('Completed DateTime', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=20),
                ColumnDefinition('Available SLA (Day)', 'integer', width=18),
                ColumnDefinition('Actual SLA (Day)', 'integer', width=16)
            ],
            parameter_count=6,
            parameter_order=['year', 'month', 'year', 'month', 'year', 'month'],
            special_processing=[]
        )
        
        # Incident Management S4
        reports['Incident Management S4'] = ReportDefinition(
            name='Incident Management S4',
            description='Incident management S4 severity tracking',
            database='cdc_poms',
            sql_file='incident_management_s4.sql',
            columns=[
                ColumnDefinition('Case Number', 'string', width=15),
                ColumnDefinition('Redmine No.', 'string', width=12),
                ColumnDefinition('Case Name        ', 'string', width=50),  # Note: exact spacing from example
                ColumnDefinition('Task Name', 'string', width=50),
                ColumnDefinition('SLA Flag', 'string', width=10),
                ColumnDefinition('Actual Start DateTime', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=20),
                ColumnDefinition('Completed DateTime', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=20),
                ColumnDefinition('Duration', 'string', width=20),
                ColumnDefinition('Available SLA (Day)', 'integer', width=18),
                ColumnDefinition('Actual SLA (Day)', 'integer', width=16),
                ColumnDefinition('Implementation Issue?', 'string', width=20),
                ColumnDefinition('Exceed SLA (Day)', 'integer', width=16)
            ],
            parameter_count=5,
            parameter_order=['year', 'month', 'year', 'month', 'year'],
            special_processing=[]
        )
        
        # Service Request
        reports['Service Request'] = ReportDefinition(
            name='Service Request',
            description='Service request tracking',
            database='cdc_poms',
            sql_file='service_request.sql',
            columns=[
                ColumnDefinition('TICKET NUMBER', 'string', width=15),
                ColumnDefinition('CATEGORY', 'string', width=20),
                ColumnDefinition('CREATED DATE', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=20),
                ColumnDefinition('REQUEST SUBJECT', 'string', width=50),
                ColumnDefinition('REASON FOR REQUEST', 'string', width=25),
                ColumnDefinition('EXPECTED START', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=18),
                ColumnDefinition('EXPECTED COMPLETED', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=20),
                ColumnDefinition('EXPECTED DURATION', 'integer', width=18),
                ColumnDefinition('ACTUAL START', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=18),
                ColumnDefinition('ACTUAL COMPLETED', 'datetime', 'yyyy-mm-dd hh:mm:ss', width=18),
                ColumnDefinition('ACTUAL DURATION', 'integer', width=16),
                ColumnDefinition('EXCEED DURATION', 'integer', width=16)
            ],
            parameter_count=2,
            parameter_order=['year', 'month'],
            special_processing=[]
        )
        
        # System Availability (Infra)
        reports['System Availability (Infra)'] = ReportDefinition(
            name='System Availability (Infra)',
            description='System availability infrastructure monitoring',
            database='cdc_poms',
            sql_file='system_availability.sql',
            columns=[
                ColumnDefinition('HOST GROUP', 'string', width=15),
                ColumnDefinition('DOWNTIME', 'string', width=20),
                ColumnDefinition('COUNTER', 'integer', width=12),
                ColumnDefinition('ALL DOWN FLAG', 'string', width=15)
            ],
            parameter_count=2,
            parameter_order=['month', 'year'],
            special_processing=[]
        )
        
        # System Performance (Response Time)
        system_perf_col1 = ColumnDefinition(
            'System Performance', 'string', width=45,
            header_style=StylingPresets.get_special_header_style(),
            data_style=StylingPresets.get_text_data_style()
        )
        system_perf_col2 = ColumnDefinition(
            'Januari 2025', 'string', width=12,  # This will be dynamic based on month/year
            header_style=StylingPresets.get_highlight_style(),
            data_style=StylingPresets.get_number_data_style()
        )
        system_perf_col3 = ColumnDefinition(
            'Unnamed: 2', 'string', width=12,
            header_style=StylingPresets.get_highlight_style(),
            data_style=StylingPresets.get_number_data_style()
        )
        system_perf_col4 = ColumnDefinition(
            'Unnamed: 3', 'string', width=12,
            header_style=StylingPresets.get_highlight_style(),
            data_style=StylingPresets.get_number_data_style()
        )
        system_perf_col5 = ColumnDefinition(
            'Unnamed: 4', 'string', width=12,
            header_style=StylingPresets.get_highlight_style(),
            data_style=StylingPresets.get_percentage_data_style()
        )

        reports['System Performance (Response Time)'] = ReportDefinition(
            name='System Performance (Response Time)',
            description='System performance response time monitoring with 16 transactions',
            database='cdc_poms',
            sql_file='system_performance.sql',
            columns=[system_perf_col1, system_perf_col2, system_perf_col3, system_perf_col4, system_perf_col5],
            parameter_count=30,  # 15 UNION blocks * 2 parameters each
            parameter_order=['month', 'year'] * 15,
            special_processing=['add_header_row', 'add_non_compliance_summary']
        )
        
        return reports
    
    def get_report(self, name: str) -> Optional[ReportDefinition]:
        """Get a report definition by name"""
        return self.reports.get(name)
    
    def get_all_reports(self) -> Dict[str, ReportDefinition]:
        """Get all report definitions"""
        return self.reports
    
    def get_report_names(self) -> List[str]:
        """Get list of all report names"""
        return list(self.reports.keys())
    
    def load_sql_query(self, sql_file: str) -> str:
        """Load SQL query from file"""
        sql_path = Path('queries') / sql_file
        if sql_path.exists():
            with open(sql_path, 'r', encoding='utf-8') as f:
                content = f.read()
                # Extract just the SQL query (remove comments)
                lines = content.split('\n')
                sql_lines = []
                for line in lines:
                    if not line.strip().startswith('--') and line.strip():
                        sql_lines.append(line)
                return '\n'.join(sql_lines).strip().rstrip(';')
        return ""


# Global registry instance
REPORT_REGISTRY = ReportRegistry()
