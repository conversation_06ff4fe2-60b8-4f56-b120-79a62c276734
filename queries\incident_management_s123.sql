-- Incident Management S123
-- Generated from: knowage\Incident Management S123.rptdesign
-- Parameters: year, month
-- Database: cdc_poms

SELECT case_number, itseverity_name, itseverity_task_number, itseverity_sla_flag, itseverity_actual_start_datetime, itseverity_completed_datetime, ROUND(HOUR(SEC_TO_TIME(itseverity_available_duration)) / 24) as itseverity_available_duration, DATEDIFF(itseverity_completed_datetime, itseverity_actual_start_datetime) AS itseverity_actual_duration 
FROM sla_itseverity 
WHERE YEAR (itseverity_case_created) = ? 
AND MONTH (itseverity_case_created) = ? 
AND YEAR (itseverity_insert_data_datetime) = ? 
AND MONTH (itseverity_insert_data_datetime) = ? 
AND YEAR (itseverity_completed_datetime) = ? 
AND MONTH (itseverity_completed_datetime) = ? 
AND deleted = 0;
