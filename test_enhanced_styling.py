#!/usr/bin/env python3
"""
Test Enhanced Styling System

This script tests the new ColumnDefinition styling capabilities to ensure:
1. Styling works correctly
2. Backward compatibility is maintained
3. Generated Excel files have proper formatting
"""

import pandas as pd
import openpyxl
from pathlib import Path
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from report_definitions import (
    ColumnDefinition, ColumnStyleFactory, StylingPresets, StylingUtils,
    ExcelCellStyle, FontStyle, FillStyle, AlignmentStyle, CellBorders,
    FontWeight, HorizontalAlignment, VerticalAlignment, BorderStyleType, FillType
)
from independent_report_generator import IndependentReportGenerator


def test_basic_styling():
    """Test basic styling functionality"""
    print("=== Testing Basic Styling ===")
    
    # Test ColumnDefinition with default styling
    col1 = ColumnDefinition('Test Column', 'string', width=20)
    assert col1.header_style is not None
    assert col1.data_style is not None
    print("✓ Default styling initialization works")
    
    # Test styling methods
    col1.set_data_font(name="Arial", size=12, color="FF000000")
    assert col1.data_style.font.name == "Arial"
    assert col1.data_style.font.size == 12
    print("✓ Font styling methods work")
    
    col1.set_data_fill("FFFF0000")
    assert col1.data_style.fill.start_color == "FFFF0000"
    print("✓ Fill styling methods work")
    
    col1.set_data_alignment(HorizontalAlignment.CENTER)
    assert col1.data_style.alignment.horizontal == HorizontalAlignment.CENTER
    print("✓ Alignment styling methods work")


def test_styling_presets():
    """Test styling presets"""
    print("\n=== Testing Styling Presets ===")
    
    # Test header style
    header_style = StylingPresets.get_header_style()
    assert header_style.font.weight == FontWeight.BOLD
    assert header_style.fill.fill_type == FillType.SOLID
    print("✓ Header style preset works")
    
    # Test percentage style
    pct_style = StylingPresets.get_percentage_data_style(2)
    assert pct_style.number_format == "0.00"
    assert pct_style.alignment.horizontal == HorizontalAlignment.RIGHT
    print("✓ Percentage style preset works")
    
    # Test datetime style
    dt_style = StylingPresets.get_datetime_data_style()
    assert dt_style.number_format == "yyyy-mm-dd hh:mm:ss"
    print("✓ Datetime style preset works")


def test_column_factory():
    """Test column factory methods"""
    print("\n=== Testing Column Factory ===")
    
    # Test text column
    text_col = ColumnStyleFactory.create_text_column("Name", width=30)
    assert text_col.data_type == 'string'
    assert text_col.width == 30
    assert text_col.data_style.alignment.horizontal == HorizontalAlignment.LEFT
    print("✓ Text column factory works")
    
    # Test percentage column
    pct_col = ColumnStyleFactory.create_percentage_column("Rate %", width=15)
    assert pct_col.data_type == 'float'
    assert pct_col.format_rule == "0.00"
    assert pct_col.data_style.number_format == "0.00"
    print("✓ Percentage column factory works")
    
    # Test number column
    num_col = ColumnStyleFactory.create_number_column("Count", width=12, decimal_places=0)
    assert num_col.data_type == 'integer'
    assert num_col.data_style.alignment.horizontal == HorizontalAlignment.RIGHT
    print("✓ Number column factory works")


def test_styling_utils():
    """Test styling utility functions"""
    print("\n=== Testing Styling Utils ===")
    
    # Test hex color validation
    assert StylingUtils.validate_hex_color("FF000000") == True
    assert StylingUtils.validate_hex_color("INVALID") == False
    print("✓ Hex color validation works")
    
    # Test RGB to hex conversion
    hex_color = StylingUtils.rgb_to_hex(255, 0, 0, 255)
    assert hex_color == "FFFF0000"
    print("✓ RGB to hex conversion works")
    
    # Test hex to RGB conversion
    rgb = StylingUtils.hex_to_rgb("FFFF0000")
    assert rgb == (255, 0, 0, 255)
    print("✓ Hex to RGB conversion works")
    
    # Test style merging
    base_style = ExcelCellStyle(font=FontStyle(name="Arial"))
    override_style = ExcelCellStyle(font=FontStyle(size=12))
    merged = StylingUtils.merge_styles(base_style, override_style)
    assert merged.font.name == "Arial"
    assert merged.font.size == 12
    print("✓ Style merging works")


def test_backward_compatibility():
    """Test backward compatibility with existing column definitions"""
    print("\n=== Testing Backward Compatibility ===")
    
    # Test old-style column definition
    old_col = ColumnDefinition('Old Column', 'string', width=20, alignment='center')
    assert old_col.data_style.alignment.horizontal == HorizontalAlignment.CENTER
    print("✓ Legacy alignment conversion works")
    
    # Test with format_rule
    old_col2 = ColumnDefinition('Old Percentage', 'float', format_rule='0.00', width=15)
    assert old_col2.format_rule == '0.00'
    print("✓ Legacy format_rule preserved")


def create_test_excel_file():
    """Create a test Excel file to validate styling"""
    print("\n=== Creating Test Excel File ===")
    
    # Create test data
    test_data = {
        'Name': ['John Doe', 'Jane Smith', 'Bob Johnson'],
        'Age': [30, 25, 35],
        'Salary': [50000.00, 60000.00, 55000.00],
        'Performance %': [85.50, 92.25, 78.75],
        'Start Date': ['2020-01-15', '2021-03-20', '2019-11-10']
    }
    df = pd.DataFrame(test_data)
    
    # Create styled columns
    columns = [
        ColumnStyleFactory.create_text_column('Name', width=20),
        ColumnStyleFactory.create_number_column('Age', width=10),
        ColumnStyleFactory.create_number_column('Salary', width=15, decimal_places=2),
        ColumnStyleFactory.create_percentage_column('Performance %', width=15),
        ColumnStyleFactory.create_date_column('Start Date', width=15)
    ]
    
    # Create test report definition
    from report_definitions import ReportDefinition
    test_report = ReportDefinition(
        name='Test Styling Report',
        description='Test report for styling validation',
        database='test',
        sql_file='test.sql',
        columns=columns,
        parameter_count=0,
        parameter_order=[],
        special_processing=[]
    )
    
    # Create output directory
    output_dir = Path('output')
    output_dir.mkdir(exist_ok=True)
    
    # Save to Excel with styling
    filepath = output_dir / 'test_styling_report.xlsx'
    
    with pd.ExcelWriter(str(filepath), engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='Test', index=False)
        
        workbook = writer.book
        worksheet = writer.sheets['Test']
        
        # Apply styling using the new system
        for col_idx, col_name in enumerate(df.columns, 1):
            # Header styling
            header_cell = worksheet.cell(row=1, column=col_idx)
            header_style = test_report.get_column_header_style(col_name)
            StylingUtils.apply_openpyxl_style(header_cell, header_style)
            
            # Data styling
            for row_idx in range(2, len(df) + 2):
                data_cell = worksheet.cell(row=row_idx, column=col_idx)
                data_style = test_report.get_column_data_style(col_name)
                StylingUtils.apply_openpyxl_style(data_cell, data_style)
            
            # Set column width
            col_def = next((col for col in columns if col.name == col_name), None)
            if col_def and col_def.width:
                col_letter = chr(64 + col_idx)
                worksheet.column_dimensions[col_letter].width = col_def.width
    
    print(f"✓ Test Excel file created: {filepath}")
    return filepath


def validate_excel_styling(filepath):
    """Validate the styling in the generated Excel file"""
    print("\n=== Validating Excel Styling ===")
    
    wb = openpyxl.load_workbook(filepath)
    ws = wb.active
    
    # Check header styling
    header_cell = ws['A1']
    assert header_cell.font.bold == True
    assert header_cell.fill.start_color.rgb == 'FF242424'
    print("✓ Header styling applied correctly")
    
    # Check data alignment
    text_cell = ws['A2']  # Name column should be left-aligned
    assert text_cell.alignment.horizontal == 'left'
    
    number_cell = ws['B2']  # Age column should be right-aligned
    assert number_cell.alignment.horizontal == 'right'
    print("✓ Data alignment applied correctly")
    
    # Check column widths
    assert ws.column_dimensions['A'].width == 20
    assert ws.column_dimensions['B'].width == 10
    print("✓ Column widths applied correctly")
    
    wb.close()


def main():
    """Run all tests"""
    print("Testing Enhanced ColumnDefinition Styling System")
    print("=" * 50)
    
    try:
        test_basic_styling()
        test_styling_presets()
        test_column_factory()
        test_styling_utils()
        test_backward_compatibility()
        
        # Create and validate test Excel file
        test_file = create_test_excel_file()
        validate_excel_styling(test_file)
        
        print("\n" + "=" * 50)
        print("🎉 ALL TESTS PASSED!")
        print("Enhanced styling system is working correctly.")
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
