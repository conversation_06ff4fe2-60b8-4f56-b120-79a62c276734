#!/usr/bin/env python3
"""
Debug telephone report formatting to understand the data flow
"""

from report_formatter import REPORT_FORMATTER
from report_definitions import REPORT_REGISTRY
import pandas as pd

def main():
    # Get the telephone report definition
    report_def = REPORT_REGISTRY.get_report('Customer Service Performance (Telephone)')
    
    print("=== Report Definition ===")
    print(f"Name: {report_def.name}")
    print("Columns:")
    for col in report_def.columns:
        print(f"  {col.name}: {col.data_type}, format_rule: {col.format_rule}")
    
    # Create sample data similar to what comes from database
    sample_data = {
        'DATE CALL': ['2025-01-01', '2025-01-02', '2025-01-03'],
        'CALL OFFER': [49, 371, 280],
        'CALL HANDLE': [49, 366, 276],
        'CALL WITHIN 10': [48, 342, 266],
        'ABANDON SHORT': [0, 1, 0],
        'ABANDON LONG': [0, 4, 4],
        'ABANDON %': [0.0, 1.35, 1.43],
        'ANSWER %': [100.0, 98.65, 98.57],
        'SLA %': [97.96, 92.45, 95.0]
    }
    
    df = pd.DataFrame(sample_data)
    print("\n=== Original Data ===")
    print(df)
    print("\nData types:")
    print(df.dtypes)
    
    # Apply formatting
    formatted_df = REPORT_FORMATTER.format_report(df, 'Customer Service Performance (Telephone)', 2025, 1)
    
    print("\n=== Formatted Data ===")
    print(formatted_df)
    print("\nData types after formatting:")
    print(formatted_df.dtypes)
    
    # Check percentage columns specifically
    percentage_cols = ['ABANDON %', 'ANSWER %', 'SLA %']
    for col in percentage_cols:
        if col in formatted_df.columns:
            print(f"\n{col} values: {formatted_df[col].tolist()}")

if __name__ == "__main__":
    main()
