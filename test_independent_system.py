#!/usr/bin/env python3
"""
Test the new independent report system
"""

from independent_report_generator import IndependentReportGenerator
import logging

logging.basicConfig(level=logging.INFO)

def main():
    generator = IndependentReportGenerator()
    
    # Test key reports
    test_reports = [
        ('System Availability (Infra)', 2025, 1),
        ('System Performance (Response Time)', 2025, 1),
        ('Service Request', 2024, 1)
    ]
    
    print("="*60)
    print("TESTING INDEPENDENT REPORT SYSTEM")
    print("="*60)
    
    for report_name, year, month in test_reports:
        print(f'\n=== Testing {report_name} ===')
        result = generator.generate_single_report(report_name, year, month)
        print(f'Success: {result["success"]}')
        print(f'Rows: {result["rows_generated"]}')
        print(f'File: {result["filename"]}')
        if not result['success']:
            print(f'Error: {result["error_message"]}')
    
    print(f'\n{"="*60}')
    print("TESTING COMPLETE")
    print("="*60)

if __name__ == "__main__":
    main()
