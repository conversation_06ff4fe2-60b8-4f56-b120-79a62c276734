#!/usr/bin/env python3
"""
Analyze example Excel file formatting
"""

import pandas as pd
import openpyxl
from openpyxl.styles import PatternFill, Font, Alignment, Border, Side

def analyze_example_file():
    """Analyze the System Performance example file"""
    
    # Load the example file
    wb = openpyxl.load_workbook('example/System Performance (Response Time)_JAN2025.xlsx')
    ws = wb.active

    print('=== EXAMPLE FILE ANALYSIS ===')
    print(f'Sheet name: {ws.title}')
    print(f'Max row: {ws.max_row}')
    print(f'Max column: {ws.max_column}')
    print()

    print('=== CELL FORMATTING ANALYSIS ===')
    for row in range(1, min(ws.max_row + 1, 20)):  # First 20 rows
        for col in range(1, min(ws.max_column + 1, 6)):  # First 6 columns
            cell = ws.cell(row=row, column=col)
            if cell.value is not None:
                print(f'Cell {cell.coordinate}: "{cell.value}"')
                if cell.fill and cell.fill.start_color.index != '00000000':
                    print(f'  Fill: {cell.fill.start_color.index}')
                if cell.font and (cell.font.bold or cell.font.color.index != '00000000'):
                    print(f'  Font: bold={cell.font.bold}, color={cell.font.color.index}')
                if any(cell.coordinate in str(merged) for merged in ws.merged_cells.ranges):
                    print(f'  Merged: Yes')
                print()

    print('=== MERGED CELLS ===')
    for merged_range in ws.merged_cells.ranges:
        print(f'Merged range: {merged_range}')
        
    print('=== DATA CONTENT ===')
    df = pd.read_excel('example/System Performance (Response Time)_JAN2025.xlsx')
    print(f'DataFrame shape: {df.shape}')
    print(f'Columns: {list(df.columns)}')
    print(df.head(10))

if __name__ == "__main__":
    analyze_example_file()
