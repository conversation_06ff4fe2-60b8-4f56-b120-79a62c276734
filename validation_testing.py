"""
Validation and Testing Module

This module provides functionality to validate generated reports against
example files and perform comprehensive testing of the report generation system.
"""

import pandas as pd
import os
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional
import numpy as np
from datetime import datetime
import json

logger = logging.getLogger(__name__)

class ReportValidator:
    """Validates generated reports against example files"""
    
    def __init__(self, example_dir: str = 'example', output_dir: str = 'output'):
        """
        Initialize the validator
        
        Args:
            example_dir: Directory containing example files
            output_dir: Directory containing generated files
        """
        self.example_dir = Path(example_dir)
        self.output_dir = Path(output_dir)
        
        if not self.example_dir.exists():
            raise ValueError(f"Example directory not found: {self.example_dir}")
    
    def get_example_files(self) -> Dict[str, Path]:
        """
        Get mapping of report names to example file paths
        
        Returns:
            Dictionary mapping report names to file paths
        """
        example_files = {}
        
        for file_path in self.example_dir.glob("*.xlsx"):
            # Extract report name from filename
            filename = file_path.stem
            # Remove date suffix (e.g., "_JAN2025")
            if '_' in filename:
                report_name = filename.rsplit('_', 1)[0]
            else:
                report_name = filename
            
            example_files[report_name] = file_path
        
        return example_files
    
    def load_excel_file(self, file_path: Path) -> pd.DataFrame:
        """
        Load Excel file into DataFrame
        
        Args:
            file_path: Path to Excel file
            
        Returns:
            DataFrame with file contents
        """
        try:
            # Try different sheet names
            excel_file = pd.ExcelFile(file_path)
            sheet_names = excel_file.sheet_names
            
            # Use first sheet or 'Sheet0' if available
            sheet_name = 'Sheet0' if 'Sheet0' in sheet_names else sheet_names[0]
            
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            logger.debug(f"Loaded {file_path} with {len(df)} rows and {len(df.columns)} columns")
            
            return df
            
        except Exception as e:
            logger.error(f"Failed to load Excel file {file_path}: {str(e)}")
            raise
    
    def compare_dataframes(self, df1: pd.DataFrame, df2: pd.DataFrame, 
                          tolerance: float = 1e-6) -> Dict[str, Any]:
        """
        Compare two DataFrames and return detailed comparison results
        
        Args:
            df1: First DataFrame (example)
            df2: Second DataFrame (generated)
            tolerance: Numerical tolerance for float comparisons
            
        Returns:
            Dictionary with comparison results
        """
        result = {
            'identical': False,
            'shape_match': False,
            'columns_match': False,
            'data_match': False,
            'differences': [],
            'summary': {}
        }
        
        # Compare shapes
        if df1.shape == df2.shape:
            result['shape_match'] = True
        else:
            result['differences'].append(f"Shape mismatch: {df1.shape} vs {df2.shape}")
        
        # Compare columns
        df1_cols = set(df1.columns)
        df2_cols = set(df2.columns)
        
        if df1_cols == df2_cols:
            result['columns_match'] = True
        else:
            missing_in_df2 = df1_cols - df2_cols
            extra_in_df2 = df2_cols - df1_cols
            
            if missing_in_df2:
                result['differences'].append(f"Columns missing in generated: {missing_in_df2}")
            if extra_in_df2:
                result['differences'].append(f"Extra columns in generated: {extra_in_df2}")
        
        # Compare data if shapes and columns match
        if result['shape_match'] and result['columns_match']:
            try:
                # Align columns
                common_cols = list(df1_cols & df2_cols)
                df1_aligned = df1[common_cols].copy()
                df2_aligned = df2[common_cols].copy()
                
                # Compare data
                data_differences = []
                
                for col in common_cols:
                    col1 = df1_aligned[col]
                    col2 = df2_aligned[col]
                    
                    # Handle different data types
                    if col1.dtype != col2.dtype:
                        data_differences.append(f"Column '{col}' type mismatch: {col1.dtype} vs {col2.dtype}")
                        continue
                    
                    # For numeric columns, use tolerance
                    if pd.api.types.is_numeric_dtype(col1):
                        if not np.allclose(col1.fillna(0), col2.fillna(0), rtol=tolerance, atol=tolerance, equal_nan=True):
                            diff_count = (~np.isclose(col1.fillna(0), col2.fillna(0), rtol=tolerance, atol=tolerance, equal_nan=True)).sum()
                            data_differences.append(f"Column '{col}' has {diff_count} numeric differences")
                    else:
                        # For non-numeric columns, exact match
                        if not col1.fillna('').equals(col2.fillna('')):
                            diff_count = (col1.fillna('') != col2.fillna('')).sum()
                            data_differences.append(f"Column '{col}' has {diff_count} text differences")
                
                if not data_differences:
                    result['data_match'] = True
                else:
                    result['differences'].extend(data_differences)
                    
            except Exception as e:
                result['differences'].append(f"Data comparison error: {str(e)}")
        
        # Overall result
        result['identical'] = result['shape_match'] and result['columns_match'] and result['data_match']
        
        # Summary statistics
        result['summary'] = {
            'example_rows': len(df1),
            'example_cols': len(df1.columns),
            'generated_rows': len(df2),
            'generated_cols': len(df2.columns),
            'total_differences': len(result['differences'])
        }
        
        return result
    
    def validate_single_report(self, report_name: str, generated_file: Optional[Path] = None) -> Dict[str, Any]:
        """
        Validate a single generated report against its example
        
        Args:
            report_name: Name of the report
            generated_file: Path to generated file (optional, will be inferred if not provided)
            
        Returns:
            Validation results dictionary
        """
        result = {
            'report_name': report_name,
            'validation_successful': False,
            'example_file_found': False,
            'generated_file_found': False,
            'comparison_result': None,
            'error_message': ''
        }
        
        try:
            # Find example file
            example_files = self.get_example_files()
            if report_name not in example_files:
                result['error_message'] = f"No example file found for report: {report_name}"
                return result
            
            example_file = example_files[report_name]
            result['example_file_found'] = True
            
            # Find generated file
            if generated_file is None:
                # Look for generated file in output directory
                pattern = f"{report_name}_*.xlsx"
                generated_files = list(self.output_dir.glob(pattern))
                
                if not generated_files:
                    result['error_message'] = f"No generated file found for report: {report_name}"
                    return result
                
                # Use the most recent file if multiple exist
                generated_file = max(generated_files, key=lambda p: p.stat().st_mtime)
            
            if not generated_file.exists():
                result['error_message'] = f"Generated file not found: {generated_file}"
                return result
            
            result['generated_file_found'] = True
            
            # Load and compare files
            example_df = self.load_excel_file(example_file)
            generated_df = self.load_excel_file(generated_file)
            
            comparison_result = self.compare_dataframes(example_df, generated_df)
            result['comparison_result'] = comparison_result
            
            result['validation_successful'] = comparison_result['identical']
            
            if not result['validation_successful']:
                result['error_message'] = f"Validation failed: {len(comparison_result['differences'])} differences found"
            
        except Exception as e:
            result['error_message'] = f"Validation error: {str(e)}"
            logger.error(f"Validation error for {report_name}: {str(e)}")
        
        return result
    
    def validate_all_reports(self, generated_files: Optional[Dict[str, Path]] = None) -> Dict[str, Any]:
        """
        Validate all generated reports against their examples
        
        Args:
            generated_files: Optional mapping of report names to generated file paths
            
        Returns:
            Overall validation results
        """
        overall_result = {
            'total_reports': 0,
            'successful_validations': 0,
            'failed_validations': 0,
            'results': [],
            'summary': {}
        }
        
        example_files = self.get_example_files()
        overall_result['total_reports'] = len(example_files)
        
        for report_name in example_files.keys():
            generated_file = None
            if generated_files and report_name in generated_files:
                generated_file = generated_files[report_name]
            
            validation_result = self.validate_single_report(report_name, generated_file)
            overall_result['results'].append(validation_result)
            
            if validation_result['validation_successful']:
                overall_result['successful_validations'] += 1
            else:
                overall_result['failed_validations'] += 1
        
        # Create summary
        overall_result['summary'] = {
            'success_rate': overall_result['successful_validations'] / overall_result['total_reports'] * 100,
            'validation_timestamp': datetime.now().isoformat()
        }
        
        return overall_result
    
    def generate_validation_report(self, validation_results: Dict[str, Any], 
                                 output_file: str = 'validation_report.json') -> None:
        """
        Generate a detailed validation report
        
        Args:
            validation_results: Results from validate_all_reports
            output_file: Output file path for the report
        """
        try:
            with open(output_file, 'w') as f:
                json.dump(validation_results, f, indent=2, default=str)
            
            logger.info(f"Validation report saved to {output_file}")
            
        except Exception as e:
            logger.error(f"Failed to save validation report: {str(e)}")
            raise
    
    def print_validation_summary(self, validation_results: Dict[str, Any]) -> None:
        """
        Print a human-readable validation summary
        
        Args:
            validation_results: Results from validate_all_reports
        """
        print("\n" + "="*60)
        print("VALIDATION SUMMARY")
        print("="*60)
        
        total = validation_results['total_reports']
        successful = validation_results['successful_validations']
        failed = validation_results['failed_validations']
        success_rate = validation_results['summary']['success_rate']
        
        print(f"Total Reports: {total}")
        print(f"Successful Validations: {successful}")
        print(f"Failed Validations: {failed}")
        print(f"Success Rate: {success_rate:.1f}%")
        
        print("\nDETAILED RESULTS:")
        print("-" * 60)
        
        for result in validation_results['results']:
            status = "✓ PASS" if result['validation_successful'] else "✗ FAIL"
            print(f"{status} {result['report_name']}")
            
            if not result['validation_successful']:
                print(f"    Error: {result['error_message']}")
                
                if result['comparison_result']:
                    comp = result['comparison_result']
                    summary = comp['summary']
                    print(f"    Example: {summary['example_rows']} rows, {summary['example_cols']} cols")
                    print(f"    Generated: {summary['generated_rows']} rows, {summary['generated_cols']} cols")
                    
                    if comp['differences']:
                        print("    Differences:")
                        for diff in comp['differences'][:3]:  # Show first 3 differences
                            print(f"      - {diff}")
                        if len(comp['differences']) > 3:
                            print(f"      ... and {len(comp['differences']) - 3} more")
                print()


def main():
    """Test the validation system"""
    logging.basicConfig(level=logging.INFO)
    
    validator = ReportValidator()
    
    print("Available example files:")
    example_files = validator.get_example_files()
    for report_name, file_path in example_files.items():
        print(f"  - {report_name}: {file_path.name}")
    
    # Test validation of example files against themselves (should pass)
    print("\nTesting validation system with example files...")
    
    # Create a test by copying an example file to output directory
    if example_files:
        test_report = list(example_files.keys())[0]
        example_file = example_files[test_report]
        
        # Create output directory
        validator.output_dir.mkdir(exist_ok=True)
        
        # Copy example file to output directory with a generated filename
        import shutil
        test_output_file = validator.output_dir / f"{test_report}_JAN2025.xlsx"
        shutil.copy2(example_file, test_output_file)
        
        # Validate
        result = validator.validate_single_report(test_report, test_output_file)
        
        print(f"\nTest validation result for {test_report}:")
        print(f"  Successful: {result['validation_successful']}")
        if not result['validation_successful']:
            print(f"  Error: {result['error_message']}")
        
        # Clean up
        test_output_file.unlink()


if __name__ == "__main__":
    main()
