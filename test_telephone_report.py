#!/usr/bin/env python3
"""
Test Customer Service Performance (Telephone) report for decimal formatting
"""

from independent_report_generator import IndependentReportGenerator
import pandas as pd
import logging

logging.basicConfig(level=logging.INFO)

def main():
    generator = IndependentReportGenerator()
    
    # Test telephone report
    print("Testing Customer Service Performance (Telephone) report...")
    result = generator.generate_single_report('Customer Service Performance (Telephone)', 2025, 1)
    
    print(f'Success: {result["success"]}')
    print(f'Rows: {result["rows_generated"]}')
    
    if result['success']:
        print(f'File: {result["filename"]}')
        
        # Check the generated file
        try:
            df = pd.read_excel(f'output/{result["filename"]}')
            print(f'Columns: {list(df.columns)}')
            print(f'Shape: {df.shape}')
            print('\nFirst few rows:')
            print(df.head())
            
            # Check for percentage columns
            percentage_cols = [col for col in df.columns if '%' in col]
            if percentage_cols:
                print(f'\nPercentage columns: {percentage_cols}')
                for col in percentage_cols:
                    print(f'{col} sample values: {df[col].head().tolist()}')
                
        except Exception as e:
            print(f'Error reading file: {e}')
    else:
        print(f'Error: {result["error_message"]}')

if __name__ == "__main__":
    main()
