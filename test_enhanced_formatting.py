#!/usr/bin/env python3
"""
Test enhanced Excel formatting across different reports
"""

from independent_report_generator import IndependentReportGenerator
import pandas as pd
import logging

logging.basicConfig(level=logging.INFO)

def test_report(generator, report_name, year, month):
    """Test a single report and show results"""
    print(f"\n=== Testing {report_name} ===")
    result = generator.generate_single_report(report_name, year, month)
    
    print(f'Success: {result["success"]}')
    if result['success']:
        print(f'Rows: {result["rows_generated"]}')
        print(f'File: {result["filename"]}')
        
        # Check the generated file
        try:
            df = pd.read_excel(f'output/{result["filename"]}')
            print(f'Shape: {df.shape}')
            print(f'Columns: {list(df.columns)}')
            
            # Show sample data
            print('Sample data:')
            print(df.head(3))
            
        except Exception as e:
            print(f'Error reading file: {e}')
    else:
        print(f'Error: {result["error_message"]}')

def main():
    generator = IndependentReportGenerator()
    
    # Test reports with enhanced formatting
    test_reports = [
        ('System Performance (Response Time)', 2025, 5),  # Test our fixed report
        ('Customer Service Performance (Telephone)', 2025, 1),  # Test percentage formatting
        ('System Availability (Infra)', 2025, 1),  # Test standard formatting
        ('Service Request', 2025, 1),  # Test standard formatting
    ]
    
    print("="*80)
    print("TESTING ENHANCED EXCEL FORMATTING")
    print("="*80)
    
    for report_name, year, month in test_reports:
        test_report(generator, report_name, year, month)
    
    print("\n" + "="*80)
    print("FORMATTING TEST COMPLETE")
    print("="*80)
    print("\nCheck the output/ folder for the generated Excel files.")
    print("The files should now have:")
    print("• Professional header styling with colors")
    print("• Proper borders on all cells")
    print("• Correct decimal formatting (2 decimal places)")
    print("• Dynamic English month names")
    print("• Enhanced visual presentation")

if __name__ == "__main__":
    main()
