#!/usr/bin/env python3
"""
Comprehensive validation of the new independent report system
"""

from independent_report_generator import IndependentReportGenerator
from validation_testing import ReportValidator
import pandas as pd
import logging
import json

logging.basicConfig(level=logging.INFO)

def main():
    print("="*80)
    print("COMPREHENSIVE VALIDATION OF INDEPENDENT REPORT SYSTEM")
    print("="*80)
    
    generator = IndependentReportGenerator()
    validator = ReportValidator()
    
    # Load the example analysis
    try:
        with open('example_analysis.json', 'r') as f:
            example_analysis = json.load(f)
    except:
        print("❌ Could not load example analysis. Run analyze_examples.py first.")
        return
    
    # Test all reports that have examples
    test_cases = [
        ('Customer Service Performance (Multiple Channel)', 2024, 1),
        ('Customer Service Performance (Telephone)', 2024, 1),
        ('Incident Management (Case Acknowledgement)', 2024, 1),
        ('Incident Management (RIT)', 2024, 1),
        ('Incident Management S123', 2024, 1),
        ('Incident Management S4', 2024, 1),
        ('Service Request', 2024, 1),
        ('System Availability (Infra)', 2025, 1),
        ('System Performance (Response Time)', 2025, 1)
    ]
    
    results = []
    
    print(f"\n📊 TESTING {len(test_cases)} REPORTS")
    print("-" * 80)
    
    for report_name, year, month in test_cases:
        print(f"\n🔄 Testing: {report_name}")
        
        # Generate the report
        result = generator.generate_single_report(report_name, year, month)
        
        test_result = {
            'report_name': report_name,
            'generation_success': result['success'],
            'rows_generated': result['rows_generated'],
            'filename': result['filename'],
            'error_message': result.get('error_message', ''),
            'validation_success': False,
            'validation_message': '',
            'column_match': False,
            'structure_match': False
        }
        
        if result['success']:
            print(f"   ✅ Generated: {result['rows_generated']} rows → {result['filename']}")
            
            # Check if we have example data for comparison
            if report_name in example_analysis:
                example_info = example_analysis[report_name]
                expected_columns = example_info['columns']
                expected_shape = example_info['shape']
                
                try:
                    # Load the generated file
                    generated_df = pd.read_excel(f"output/{result['filename']}")
                    
                    # Compare structure
                    structure_match = generated_df.shape == tuple(expected_shape)
                    column_match = list(generated_df.columns) == expected_columns
                    
                    test_result['structure_match'] = structure_match
                    test_result['column_match'] = column_match
                    
                    print(f"   📏 Shape: Generated {generated_df.shape} vs Expected {expected_shape} {'✅' if structure_match else '❌'}")
                    print(f"   📋 Columns: {'✅' if column_match else '❌'}")
                    
                    if not column_match:
                        print(f"      Generated: {list(generated_df.columns)}")
                        print(f"      Expected:  {expected_columns}")
                    
                    # Overall validation
                    if structure_match and column_match:
                        test_result['validation_success'] = True
                        test_result['validation_message'] = "Perfect match!"
                        print(f"   🎯 Validation: ✅ PERFECT MATCH")
                    else:
                        issues = []
                        if not structure_match:
                            issues.append("shape mismatch")
                        if not column_match:
                            issues.append("column mismatch")
                        test_result['validation_message'] = f"Issues: {', '.join(issues)}"
                        print(f"   🎯 Validation: ❌ {test_result['validation_message']}")
                        
                except Exception as e:
                    test_result['validation_message'] = f"Validation error: {str(e)}"
                    print(f"   ❌ Validation error: {str(e)}")
            else:
                test_result['validation_message'] = "No example file for comparison"
                print(f"   ⚠️  No example file for comparison")
        else:
            print(f"   ❌ Generation failed: {result['error_message']}")
        
        results.append(test_result)
    
    # Summary
    print(f"\n{'='*80}")
    print("VALIDATION SUMMARY")
    print("="*80)
    
    successful_generations = sum(1 for r in results if r['generation_success'])
    successful_validations = sum(1 for r in results if r['validation_success'])
    perfect_matches = sum(1 for r in results if r['validation_success'] and r['structure_match'] and r['column_match'])
    
    print(f"\n📈 GENERATION RESULTS:")
    print(f"   ✅ Successful: {successful_generations}/{len(results)}")
    print(f"   ❌ Failed: {len(results) - successful_generations}/{len(results)}")
    
    print(f"\n🎯 VALIDATION RESULTS:")
    print(f"   ✅ Perfect matches: {perfect_matches}/{len(results)}")
    print(f"   ⚠️  Partial matches: {successful_validations - perfect_matches}/{len(results)}")
    print(f"   ❌ Failed validations: {len(results) - successful_validations}/{len(results)}")
    
    print(f"\n📋 DETAILED RESULTS:")
    for result in results:
        status = "✅" if result['validation_success'] else "❌" if result['generation_success'] else "💥"
        print(f"   {status} {result['report_name']}")
        if result['generation_success']:
            print(f"      Generated: {result['rows_generated']} rows")
            if result['validation_message']:
                print(f"      Validation: {result['validation_message']}")
        else:
            print(f"      Error: {result['error_message']}")
    
    print(f"\n{'='*80}")
    print("SYSTEM STATUS")
    print("="*80)
    
    if perfect_matches == len(results):
        print("🎉 EXCELLENT! All reports generate with perfect structure and column matching!")
    elif successful_generations == len(results):
        print("✅ GOOD! All reports generate successfully. Some formatting adjustments needed.")
    else:
        print("⚠️  PARTIAL SUCCESS. Some reports need debugging.")
    
    print(f"\n🔧 FEATURES IMPLEMENTED:")
    features = [
        "✅ Independent report system (no BIRT dependency)",
        "✅ SQL queries extracted to individual files",
        "✅ Report definitions with exact column mappings",
        "✅ Enhanced formatting engine",
        "✅ MTD calculations for telephone reports",
        "✅ Summary rows for performance reports",
        "✅ Proper Excel formatting and column widths",
        "✅ Comprehensive validation system"
    ]
    
    for feature in features:
        print(f"   {feature}")
    
    # Save results
    with open('validation_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Results saved to validation_results.json")

if __name__ == "__main__":
    main()
