# POMS Report Generator - Installation Guide

This guide provides step-by-step instructions for installing and configuring the POMS Report Generator.

## System Requirements

- **Operating System**: Windows 10/11, Linux, or macOS
- **Python**: Version 3.8 or higher
- **Memory**: Minimum 4GB RAM (8GB recommended)
- **Storage**: At least 1GB free space
- **Network**: Access to MySQL database servers

## Installation Steps

### Step 1: Python Installation

#### Windows
1. Download Python from [python.org](https://python.org/downloads/)
2. Run the installer and **check "Add Python to PATH"**
3. Verify installation:
   ```cmd
   python --version
   ```

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install python3 python3-pip
```

#### macOS
```bash
# Using Homebrew
brew install python3
```

### Step 2: Download Application Files

1. Extract the POMS Report Generator files to a directory, e.g.:
   - Windows: `C:\POMS_Report_Generator`
   - Linux/macOS: `~/POMS_Report_Generator`

2. Navigate to the application directory:
   ```bash
   cd /path/to/POMS_Report_Generator
   ```

### Step 3: Install Dependencies

Run the following command in the application directory:

#### Windows
```cmd
pip install -r requirements.txt
```

#### Linux/macOS
```bash
pip3 install -r requirements.txt
```

### Step 4: Database Configuration

1. Open `config.yaml` in a text editor
2. Update the database connection settings:

```yaml
database:
  cdccrm:
    host: "your-database-server-ip"
    port: 3306
    database: "cdccrm"
    username: "your-username"
    password: "your-password"
  cdc_poms:
    host: "your-database-server-ip"
    port: 3306
    database: "cdc_poms"
    username: "your-username"
    password: "your-password"
```

3. Save the file

### Step 5: Test Installation

#### Windows
```cmd
python database_manager.py
```

#### Linux/macOS
```bash
python3 database_manager.py
```

You should see database connection test results.

## Running the Application

### Option 1: Easy Launcher (Recommended)

#### Windows
Double-click `run_poms_generator.bat` or run:
```cmd
run_poms_generator.bat
```

#### Linux/macOS
```bash
./run_poms_generator.sh
```

### Option 2: Direct Python Execution

#### GUI Mode (Default)
```bash
python main.py
```

#### Command Line Mode
```bash
# List available reports
python main.py --cli --list-reports

# Test database connections
python main.py --cli --test-connections

# Generate all reports
python main.py --cli --generate --all-reports --year 2025 --month 1

# Generate specific reports
python main.py --cli --generate --reports "Service Request" --year 2025 --month 1
```

## Configuration Details

### Database Settings

The application supports two databases:

1. **cdccrm**: Main CRM database
   - Contains customer service, incident management data
   - Tables: `sla_cs`, `sla_itspec`, `sla_byapprover`, etc.

2. **cdc_poms**: POMS monitoring database
   - Contains system availability and performance data
   - Tables: `sla_nagios`, `sla_response_time`

### Application Settings

```yaml
application:
  output_directory: "./output"      # Where reports are saved
  log_directory: "./logs"           # Where log files are stored
  log_level: "INFO"                # Logging level
  max_concurrent_reports: 3         # Max parallel report generation
```

### Report Settings

```yaml
reports:
  file_naming:
    date_format: "%b%Y"            # Month format (JAN2025)
    separator: "_"                 # Filename separator
  timeout_seconds: 300             # Query timeout (5 minutes)
```

## Troubleshooting

### Common Installation Issues

#### 1. Python Not Found
**Error**: `'python' is not recognized as an internal or external command`

**Solution**:
- Reinstall Python and check "Add Python to PATH"
- Or use full path: `C:\Python39\python.exe`

#### 2. Permission Denied
**Error**: `Permission denied` when installing packages

**Solution**:
```bash
# Linux/macOS
sudo pip3 install -r requirements.txt

# Windows (run as Administrator)
pip install -r requirements.txt
```

#### 3. Module Not Found
**Error**: `ModuleNotFoundError: No module named 'pandas'`

**Solution**:
```bash
pip install pandas mysql-connector-python PyYAML openpyxl ttkbootstrap
```

### Database Connection Issues

#### 1. Connection Timeout
**Error**: `Can't connect to MySQL server`

**Solutions**:
- Verify database server IP and port
- Check firewall settings
- Ensure database server is running
- Test connection with MySQL client

#### 2. Authentication Failed
**Error**: `Access denied for user`

**Solutions**:
- Verify username and password
- Check user permissions in MySQL
- Ensure user has SELECT privileges on required tables

#### 3. Database Not Found
**Error**: `Unknown database`

**Solutions**:
- Verify database name in config.yaml
- Check if database exists on server
- Ensure user has access to the database

### Application Issues

#### 1. GUI Won't Start
**Error**: GUI window doesn't appear

**Solutions**:
- Check if tkinter is installed: `python -c "import tkinter"`
- Install tkinter: `sudo apt install python3-tk` (Linux)
- Try command-line mode: `python main.py --cli --list-reports`

#### 2. Reports Not Generated
**Error**: No output files created

**Solutions**:
- Check database connections
- Verify output directory permissions
- Review log files in `logs/` directory
- Ensure sufficient disk space

#### 3. Validation Failures
**Error**: Generated reports don't match examples

**Solutions**:
- Check database data for the specified month/year
- Verify SQL queries are returning expected results
- Compare column names and data types
- Review formatting logic in report generator

## Advanced Configuration

### Custom Output Directory

Set a custom output directory:

```yaml
application:
  output_directory: "C:/Reports/POMS"  # Windows
  # or
  output_directory: "/home/<USER>/reports"  # Linux
```

### Logging Configuration

Adjust logging levels:

```yaml
application:
  log_level: "DEBUG"    # More detailed logs
  log_level: "WARNING"  # Only warnings and errors
  log_level: "ERROR"    # Only errors
```

### Database Connection Pooling

For better performance with multiple reports:

```yaml
application:
  max_concurrent_reports: 5  # Increase for faster generation
```

## Security Considerations

1. **Database Passwords**: Store securely, consider environment variables
2. **Network Access**: Ensure database servers are properly secured
3. **File Permissions**: Restrict access to configuration files
4. **Log Files**: May contain sensitive information, secure appropriately

## Performance Optimization

1. **Database Indexing**: Ensure proper indexes on date columns
2. **Network**: Use fast, stable network connection to database
3. **Hardware**: More RAM and faster CPU improve performance
4. **Concurrent Reports**: Adjust `max_concurrent_reports` based on system capacity

## Support and Maintenance

### Regular Maintenance

1. **Log Rotation**: Clean up old log files periodically
2. **Output Cleanup**: Archive or delete old report files
3. **Database Maintenance**: Ensure database performance is optimal
4. **Dependency Updates**: Keep Python packages updated

### Backup Recommendations

1. **Configuration**: Backup `config.yaml` before changes
2. **BIRT Files**: Keep original `.rptdesign` files safe
3. **Example Files**: Maintain example files for validation

### Getting Help

1. Check log files in `logs/` directory
2. Run validation tests: `python main.py --cli --validate`
3. Test database connections: `python main.py --cli --test-connections`
4. Review this installation guide
5. Check README.md for usage instructions
