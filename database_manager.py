"""
Database Manager Module

This module handles database connections and query execution for the POMS report generator.
"""

import mysql.connector
from mysql.connector import Error
import pandas as pd
import yaml
import logging
from typing import Dict, List, Optional, Any
from contextlib import contextmanager
import time

logger = logging.getLogger(__name__)

class DatabaseManager:
    """Manages database connections and query execution"""
    
    def __init__(self, config_path: str = 'config.yaml'):
        """
        Initialize the database manager with configuration
        
        Args:
            config_path: Path to the configuration file
        """
        self.config = self._load_config(config_path)
        self.connections = {}
        self._connection_pools = {}
    
    def _load_config(self, config_path: str) -> Dict:
        """Load configuration from YAML file"""
        try:
            with open(config_path, 'r') as file:
                config = yaml.safe_load(file)
                logger.info(f"Configuration loaded from {config_path}")
                return config
        except Exception as e:
            logger.error(f"Failed to load configuration from {config_path}: {str(e)}")
            raise
    
    def get_database_config(self, database_name: str) -> Dict:
        """
        Get database configuration for a specific database
        
        Args:
            database_name: Name of the database (e.g., 'cdccrm', 'cdc_poms')
            
        Returns:
            Database configuration dictionary
        """
        db_configs = self.config.get('database', {})
        if database_name not in db_configs:
            raise ValueError(f"Database configuration not found for: {database_name}")
        
        return db_configs[database_name]
    
    @contextmanager
    def get_connection(self, database_name: str):
        """
        Get a database connection using context manager
        
        Args:
            database_name: Name of the database
            
        Yields:
            MySQL connection object
        """
        connection = None
        try:
            config = self.get_database_config(database_name)
            
            connection = mysql.connector.connect(
                host=config['host'],
                port=config['port'],
                database=config['database'],
                user=config['username'],
                password=config['password'],
                autocommit=True,
                charset='utf8mb4',
                use_unicode=True,
                connect_timeout=30,
                sql_mode='TRADITIONAL'
            )
            
            if connection.is_connected():
                logger.debug(f"Connected to {database_name} database")
                yield connection
            else:
                raise Error("Failed to establish connection")
                
        except Error as e:
            logger.error(f"Database connection error for {database_name}: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error connecting to {database_name}: {str(e)}")
            raise
        finally:
            if connection and connection.is_connected():
                connection.close()
                logger.debug(f"Connection to {database_name} closed")
    
    def execute_query(self, database_name: str, query: str, parameters: Optional[List] = None) -> pd.DataFrame:
        """
        Execute a SQL query and return results as a pandas DataFrame

        Args:
            database_name: Name of the database
            query: SQL query to execute
            parameters: Query parameters (for prepared statements)

        Returns:
            pandas DataFrame with query results
        """
        try:
            with self.get_connection(database_name) as connection:
                logger.info(f"Executing query on {database_name}")
                logger.debug(f"Query: {query[:100]}...")

                start_time = time.time()

                # Convert ? placeholders to %s for MySQL connector
                mysql_query = query.replace('?', '%s')

                # Manual cursor approach for better control
                cursor = connection.cursor()

                # Count actual placeholders in query
                placeholder_count = query.count('?')

                # Execute query with parameters
                if parameters and placeholder_count > 0:
                    # Ensure we have the right number of parameters
                    if len(parameters) != placeholder_count:
                        logger.warning(f"Parameter count mismatch: query has {placeholder_count} placeholders, "
                                     f"but {len(parameters)} parameters provided. Adjusting...")
                        # Adjust parameters to match placeholders
                        if len(parameters) > placeholder_count:
                            parameters = parameters[:placeholder_count]
                        else:
                            # Repeat parameters if we have fewer than needed
                            while len(parameters) < placeholder_count:
                                parameters.extend(parameters[:placeholder_count - len(parameters)])

                    cursor.execute(mysql_query, tuple(parameters))
                else:
                    cursor.execute(mysql_query)

                # Fetch results
                columns = [desc[0] for desc in cursor.description]
                rows = cursor.fetchall()
                cursor.close()

                # Create DataFrame
                df = pd.DataFrame(rows, columns=columns)

                execution_time = time.time() - start_time
                logger.info(f"Query executed successfully in {execution_time:.2f} seconds. Rows returned: {len(df)}")

                return df

        except Error as e:
            logger.error(f"Database query error: {str(e)}")
            logger.error(f"Query: {query}")
            logger.error(f"Parameters: {parameters}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error executing query: {str(e)}")
            raise
    
    def test_connection(self, database_name: str) -> bool:
        """
        Test database connection
        
        Args:
            database_name: Name of the database to test
            
        Returns:
            True if connection successful, False otherwise
        """
        try:
            with self.get_connection(database_name) as connection:
                cursor = connection.cursor()
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                cursor.close()
                
                if result and result[0] == 1:
                    logger.info(f"Connection test successful for {database_name}")
                    return True
                else:
                    logger.warning(f"Connection test failed for {database_name}")
                    return False
                    
        except Exception as e:
            logger.error(f"Connection test failed for {database_name}: {str(e)}")
            return False
    
    def test_all_connections(self) -> Dict[str, bool]:
        """
        Test all configured database connections
        
        Returns:
            Dictionary mapping database names to connection status
        """
        results = {}
        db_configs = self.config.get('database', {})
        
        for db_name in db_configs.keys():
            results[db_name] = self.test_connection(db_name)
        
        return results
    
    def get_table_info(self, database_name: str, table_name: str) -> Dict:
        """
        Get information about a table structure
        
        Args:
            database_name: Name of the database
            table_name: Name of the table
            
        Returns:
            Dictionary with table information
        """
        try:
            # Get column information
            columns_query = """
            SELECT 
                COLUMN_NAME,
                DATA_TYPE,
                IS_NULLABLE,
                COLUMN_DEFAULT,
                COLUMN_COMMENT
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = %s AND TABLE_NAME = %s
            ORDER BY ORDINAL_POSITION
            """
            
            config = self.get_database_config(database_name)
            columns_df = self.execute_query(
                database_name, 
                columns_query, 
                [config['database'], table_name]
            )
            
            # Get row count
            count_query = f"SELECT COUNT(*) as row_count FROM {table_name}"
            count_df = self.execute_query(database_name, count_query)
            
            return {
                'table_name': table_name,
                'database': database_name,
                'columns': columns_df.to_dict('records'),
                'row_count': count_df.iloc[0]['row_count'] if not count_df.empty else 0
            }
            
        except Exception as e:
            logger.error(f"Failed to get table info for {database_name}.{table_name}: {str(e)}")
            raise
    
    def validate_query_parameters(self, query: str, parameters: List) -> bool:
        """
        Validate that the number of parameters matches the query placeholders
        
        Args:
            query: SQL query string
            parameters: List of parameters
            
        Returns:
            True if parameters match, False otherwise
        """
        placeholder_count = query.count('?')
        parameter_count = len(parameters) if parameters else 0
        
        if placeholder_count != parameter_count:
            logger.error(f"Parameter count mismatch. Query has {placeholder_count} placeholders, "
                        f"but {parameter_count} parameters provided")
            return False
        
        return True


def main():
    """Test the database manager"""
    logging.basicConfig(level=logging.INFO)
    
    db_manager = DatabaseManager()
    
    # Test all connections
    print("Testing database connections...")
    results = db_manager.test_all_connections()
    
    for db_name, status in results.items():
        status_text = "✓ SUCCESS" if status else "✗ FAILED"
        print(f"  {db_name}: {status_text}")
    
    # Test a simple query if connections work
    if any(results.values()):
        print("\nTesting sample queries...")
        
        for db_name, status in results.items():
            if status:
                try:
                    # Test with a simple query
                    df = db_manager.execute_query(
                        db_name, 
                        "SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = %s",
                        [db_manager.get_database_config(db_name)['database']]
                    )
                    print(f"  {db_name}: Found {df.iloc[0]['table_count']} tables")
                except Exception as e:
                    print(f"  {db_name}: Query test failed - {str(e)}")


if __name__ == "__main__":
    main()
