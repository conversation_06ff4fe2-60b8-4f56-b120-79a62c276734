#!/usr/bin/env python3
"""
Test the column mapping fixes
"""

from independent_report_generator import IndependentReportGenerator
import pandas as pd
import logging

logging.basicConfig(level=logging.INFO)

def main():
    generator = IndependentReportGenerator()
    
    # Test the fixed reports
    test_reports = [
        ('Customer Service Performance (Multiple Channel)', 2024, 1),
        ('Service Request', 2024, 1),
        ('System Performance (Response Time)', 2025, 1)
    ]
    
    for report_name, year, month in test_reports:
        print(f'\n=== Testing {report_name} ===')
        result = generator.generate_single_report(report_name, year, month)
        print(f'Success: {result["success"]}')
        print(f'Rows: {result["rows_generated"]}')
        if result['success']:
            print(f'File: {result["filename"]}')
            
            # Check columns
            df = pd.read_excel(f'output/{result["filename"]}')
            print(f'Columns: {list(df.columns)}')
            print(f'Shape: {df.shape}')
        else:
            print(f'Error: {result["error_message"]}')

if __name__ == "__main__":
    main()
