[{"report_name": "Customer Service Performance (Multiple Channel)", "generation_success": true, "rows_generated": 4021, "filename": "Customer Service Performance (Multiple Channel)_JAN2024.xlsx", "error_message": "", "validation_success": false, "validation_message": "Issues: shape mismatch", "column_match": true, "structure_match": false}, {"report_name": "Customer Service Performance (Telephone)", "generation_success": true, "rows_generated": 32, "filename": "Customer Service Performance (Telephone)_JAN2024.xlsx", "error_message": "", "validation_success": true, "validation_message": "Perfect match!", "column_match": true, "structure_match": true}, {"report_name": "Incident Management (Case Acknowledgement)", "generation_success": true, "rows_generated": 1555, "filename": "Incident Management (Case Acknowledgement)_JAN2024.xlsx", "error_message": "", "validation_success": false, "validation_message": "Issues: shape mismatch", "column_match": true, "structure_match": false}, {"report_name": "Incident Management (RIT)", "generation_success": true, "rows_generated": 1479, "filename": "Incident Management (RIT)_JAN2024.xlsx", "error_message": "", "validation_success": false, "validation_message": "Issues: shape mismatch", "column_match": true, "structure_match": false}, {"report_name": "Incident Management S123", "generation_success": true, "rows_generated": 1657, "filename": "Incident Management S123_JAN2024.xlsx", "error_message": "", "validation_success": false, "validation_message": "Issues: shape mismatch", "column_match": true, "structure_match": false}, {"report_name": "Incident Management S4", "generation_success": true, "rows_generated": 2, "filename": "Incident Management S4_JAN2024.xlsx", "error_message": "", "validation_success": false, "validation_message": "Issues: shape mismatch, column mismatch", "column_match": false, "structure_match": false}, {"report_name": "Service Request", "generation_success": true, "rows_generated": 1, "filename": "Service Request_JAN2024.xlsx", "error_message": "", "validation_success": true, "validation_message": "Perfect match!", "column_match": true, "structure_match": true}, {"report_name": "System Availability (Infra)", "generation_success": true, "rows_generated": 174, "filename": "System Availability (Infra)_JAN2025.xlsx", "error_message": "", "validation_success": true, "validation_message": "Perfect match!", "column_match": true, "structure_match": true}, {"report_name": "System Performance (Response Time)", "generation_success": true, "rows_generated": 18, "filename": "System Performance (Response Time)_JAN2025.xlsx", "error_message": "", "validation_success": false, "validation_message": "Issues: column mismatch", "column_match": false, "structure_match": true}]