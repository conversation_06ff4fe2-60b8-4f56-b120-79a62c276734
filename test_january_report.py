#!/usr/bin/env python3
"""
Test System Performance report for January 2025 to verify date display
"""

from independent_report_generator import IndependentReportGenerator
import pandas as pd
import logging

logging.basicConfig(level=logging.INFO)

def main():
    generator = IndependentReportGenerator()
    
    # Test January 2025 report
    print("Testing System Performance report for January 2025...")
    result = generator.generate_single_report('System Performance (Response Time)', 2025, 1)
    
    print(f'Success: {result["success"]}')
    print(f'Rows: {result["rows_generated"]}')
    
    if result['success']:
        print(f'File: {result["filename"]}')
        
        # Check the generated file
        try:
            df = pd.read_excel(f'output/{result["filename"]}')
            print(f'Columns: {list(df.columns)}')
            print('\nFirst few rows:')
            print(df.head())
            
            # Check for decimal formatting in Exceed % column
            if 'Unnamed: 4' in df.columns:
                print('\nExceed % column values:')
                print(df['Unnamed: 4'].head(10))
                
        except Exception as e:
            print(f'Error reading file: {e}')
    else:
        print(f'Error: {result["error_message"]}')

if __name__ == "__main__":
    main()
