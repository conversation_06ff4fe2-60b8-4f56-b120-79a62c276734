"""
Configuration Manager

This module handles configuration file management, validation, and provides
a centralized way to access application settings.
"""

import yaml
import os
import logging
from typing import Dict, Any, Optional
from pathlib import Path
import shutil
from datetime import datetime

logger = logging.getLogger(__name__)

class ConfigManager:
    """Manages application configuration"""
    
    def __init__(self, config_path: str = 'config.yaml'):
        """
        Initialize configuration manager
        
        Args:
            config_path: Path to the configuration file
        """
        self.config_path = Path(config_path)
        self.config = {}
        self.load_config()
    
    def load_config(self) -> None:
        """Load configuration from file"""
        try:
            if not self.config_path.exists():
                logger.warning(f"Configuration file not found: {self.config_path}")
                self.create_default_config()
            
            with open(self.config_path, 'r', encoding='utf-8') as file:
                self.config = yaml.safe_load(file) or {}
            
            # Validate configuration
            self.validate_config()
            
            logger.info(f"Configuration loaded successfully from {self.config_path}")
            
        except Exception as e:
            logger.error(f"Failed to load configuration: {str(e)}")
            raise
    
    def save_config(self) -> None:
        """Save current configuration to file"""
        try:
            # Create backup
            self.backup_config()
            
            with open(self.config_path, 'w', encoding='utf-8') as file:
                yaml.dump(self.config, file, default_flow_style=False, indent=2)
            
            logger.info(f"Configuration saved to {self.config_path}")
            
        except Exception as e:
            logger.error(f"Failed to save configuration: {str(e)}")
            raise
    
    def backup_config(self) -> None:
        """Create a backup of the current configuration file"""
        if self.config_path.exists():
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = self.config_path.with_suffix(f'.backup_{timestamp}.yaml')
            shutil.copy2(self.config_path, backup_path)
            logger.debug(f"Configuration backed up to {backup_path}")
    
    def create_default_config(self) -> None:
        """Create default configuration file"""
        default_config = {
            'database': {
                'cdc_poms': {
                    'host': '*************',
                    'port': 3306,
                    'database': 'cdc_poms',
                    'username': 'poms_user',
                    'password': 'cDcPoms@2019'
                }
            },
            'application': {
                'output_directory': './output',
                'log_directory': './logs',
                'log_level': 'INFO',
                'max_concurrent_reports': 3
            },
            'reports': {
                'file_naming': {
                    'date_format': '%b%Y',
                    'separator': '_'
                },
                'timeout_seconds': 300,
                'definitions': {
                    'Customer Service Performance (Multiple Channel)': {
                        'file_pattern': 'Customer Service Performance (Multiple Channel)_{date}.xlsx',
                        'database': 'cdc_poms',
                        'table': 'sla_cs'
                    },
                    'Customer Service Performance (Telephone)': {
                        'file_pattern': 'Customer Service Performance (Telephone)_{date}.xlsx',
                        'database': 'cdc_poms',
                        'table': 'sla_mitel'
                    },
                    'Incident Management (Case Acknowledgement)': {
                        'file_pattern': 'Incident Management (Case Acknowledgement)_{date}.xlsx',
                        'database': 'cdc_poms',
                        'table': 'sla_cs'
                    },
                    'Incident Management (RIT)': {
                        'file_pattern': 'Incident Management (RIT)_{date}.xlsx',
                        'database': 'cdc_poms',
                        'table': 'sla_itspec'
                    },
                    'Incident Management S4': {
                        'file_pattern': 'Incident Management S4_{date}.xlsx',
                        'database': 'cdc_poms',
                        'table': 'sla_byapprover'
                    },
                    'Incident Management S123': {
                        'file_pattern': 'Incident Management S123_{date}.xlsx',
                        'database': 'cdc_poms',
                        'table': 'sla_itseverity'
                    },
                    'Service Request': {
                        'file_pattern': 'Service Request_{date}.xlsx',
                        'database': 'cdc_poms',
                        'table': 'sla_itservice_request'
                    },
                    'System Availability (Infra)': {
                        'file_pattern': 'System Availability (Infra)_{date}.xlsx',
                        'database': 'cdc_poms',
                        'table': 'sla_nagios'
                    },
                    'System Performance (Response Time)': {
                        'file_pattern': 'System Performance (Response Time)_{date}.xlsx',
                        'database': 'cdc_poms',
                        'table': 'sla_response_time'
                    }
                }
            }
        }
        
        self.config = default_config
        self.save_config()
        logger.info("Default configuration created")
    
    def validate_config(self) -> None:
        """Validate configuration structure and values"""
        required_sections = ['database', 'application', 'reports']
        
        for section in required_sections:
            if section not in self.config:
                raise ValueError(f"Missing required configuration section: {section}")
        
        # Validate database section
        if not isinstance(self.config['database'], dict):
            raise ValueError("Database configuration must be a dictionary")
        
        for db_name, db_config in self.config['database'].items():
            required_db_fields = ['host', 'port', 'database', 'username', 'password']
            for field in required_db_fields:
                if field not in db_config:
                    raise ValueError(f"Missing required database field '{field}' for database '{db_name}'")
        
        # Validate application section
        app_config = self.config['application']
        if 'output_directory' not in app_config:
            app_config['output_directory'] = './output'
        
        if 'log_level' not in app_config:
            app_config['log_level'] = 'INFO'
        
        # Create directories if they don't exist
        output_dir = Path(app_config['output_directory'])
        output_dir.mkdir(exist_ok=True)
        
        if 'log_directory' in app_config:
            log_dir = Path(app_config['log_directory'])
            log_dir.mkdir(exist_ok=True)
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """
        Get configuration value using dot notation
        
        Args:
            key_path: Dot-separated path to the configuration value (e.g., 'database.cdccrm.host')
            default: Default value if key not found
            
        Returns:
            Configuration value or default
        """
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path: str, value: Any) -> None:
        """
        Set configuration value using dot notation
        
        Args:
            key_path: Dot-separated path to the configuration value
            value: Value to set
        """
        keys = key_path.split('.')
        config_ref = self.config
        
        # Navigate to the parent of the target key
        for key in keys[:-1]:
            if key not in config_ref:
                config_ref[key] = {}
            config_ref = config_ref[key]
        
        # Set the value
        config_ref[keys[-1]] = value
    
    def get_database_config(self, database_name: str) -> Dict[str, Any]:
        """
        Get database configuration
        
        Args:
            database_name: Name of the database
            
        Returns:
            Database configuration dictionary
        """
        db_config = self.get(f'database.{database_name}')
        if db_config is None:
            raise ValueError(f"Database configuration not found: {database_name}")
        return db_config
    
    def get_report_config(self, report_name: str) -> Dict[str, Any]:
        """
        Get report configuration
        
        Args:
            report_name: Name of the report
            
        Returns:
            Report configuration dictionary
        """
        return self.get(f'reports.definitions.{report_name}', {})
    
    def get_all_database_names(self) -> list:
        """Get list of all configured database names"""
        return list(self.config.get('database', {}).keys())
    
    def get_all_report_names(self) -> list:
        """Get list of all configured report names"""
        return list(self.config.get('reports', {}).get('definitions', {}).keys())
    
    def update_database_config(self, database_name: str, config_updates: Dict[str, Any]) -> None:
        """
        Update database configuration
        
        Args:
            database_name: Name of the database
            config_updates: Dictionary of configuration updates
        """
        current_config = self.get_database_config(database_name)
        current_config.update(config_updates)
        self.set(f'database.{database_name}', current_config)
    
    def export_config(self, export_path: str) -> None:
        """
        Export configuration to a file
        
        Args:
            export_path: Path to export the configuration
        """
        with open(export_path, 'w', encoding='utf-8') as file:
            yaml.dump(self.config, file, default_flow_style=False, indent=2)
        logger.info(f"Configuration exported to {export_path}")
    
    def import_config(self, import_path: str) -> None:
        """
        Import configuration from a file
        
        Args:
            import_path: Path to import the configuration from
        """
        with open(import_path, 'r', encoding='utf-8') as file:
            imported_config = yaml.safe_load(file)
        
        # Backup current config before importing
        self.backup_config()
        
        self.config = imported_config
        self.validate_config()
        self.save_config()
        
        logger.info(f"Configuration imported from {import_path}")


def main():
    """Test the configuration manager"""
    logging.basicConfig(level=logging.INFO)
    
    config_manager = ConfigManager()
    
    print("Configuration loaded successfully!")
    print(f"Available databases: {config_manager.get_all_database_names()}")
    print(f"Available reports: {len(config_manager.get_all_report_names())}")
    
    # Test getting values
    print(f"Output directory: {config_manager.get('application.output_directory')}")
    print(f"Log level: {config_manager.get('application.log_level')}")
    
    # Test database config
    try:
        cdccrm_config = config_manager.get_database_config('cdccrm')
        print(f"CDCCRM host: {cdccrm_config['host']}")
    except Exception as e:
        print(f"Error getting database config: {e}")


if __name__ == "__main__":
    main()
