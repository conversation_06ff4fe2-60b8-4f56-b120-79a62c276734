# POMS Report Generator Configuration

# Database Configuration
database:
  # POMS database (cdc_poms)
  cdc_poms:
    host: "*************"
    port: 3306
    database: "cdc_poms"
    username: "poms_user"
    password: "cDcPoms@2019"  # Base64 decoded: Y0RjUG9tc0AyMDE5

# Application Settings
application:
  output_directory: "./output"
  log_directory: "./logs"
  log_level: "INFO"
  max_concurrent_reports: 3
  
# Report Configuration
reports:
  file_naming:
    date_format: "%b%Y"  # JAN2025 format
    separator: "_"
  
  timeout_seconds: 300  # 5 minutes per report
  
  # Report definitions with their corresponding files and databases
  definitions:
    "Customer Service Performance (Multiple Channel)":
      file_pattern: "Customer Service Performance (Multiple Channel)_{date}.xlsx"
      database: "cdc_poms"
      table: "sla_cs"
      
    "Customer Service Performance (Telephone)":
      file_pattern: "Customer Service Performance (Telephone)_{date}.xlsx"
      database: "cdc_poms"
      table: "sla_mitel"
      
    "Incident Management (Case Acknowledgement)":
      file_pattern: "Incident Management (Case Acknowledgement)_{date}.xlsx"
      database: "cdc_poms"
      table: "sla_cs"
      
    "Incident Management (RIT)":
      file_pattern: "Incident Management (RIT)_{date}.xlsx"
      database: "cdc_poms"
      table: "sla_itspec"
      
    "Incident Management S4":
      file_pattern: "Incident Management S4_{date}.xlsx"
      database: "cdc_poms"
      table: "sla_byapprover"
      
    "Incident Management S123":
      file_pattern: "Incident Management S123_{date}.xlsx"
      database: "cdc_poms"
      table: "sla_itseverity"
      
    "Service Request":
      file_pattern: "Service Request_{date}.xlsx"
      database: "cdc_poms"
      table: "sla_itservice_request"
      
    "System Availability (Infra)":
      file_pattern: "System Availability (Infra)_{date}.xlsx"
      database: "cdc_poms"
      table: "sla_nagios"
      
    "System Performance (Response Time)":
      file_pattern: "System Performance (Response Time)_{date}.xlsx"
      database: "cdc_poms"
      table: "sla_response_time"
