-- Incident Management S4
-- Generated from: knowage\Incident Management S4.rptdesign
-- Parameters: year, month
-- Database: cdc_poms

SELECT
  case_number,
  redmine_number,
  itapprover_name AS case_title,
  itapprover_task_name AS task_title,
  itapprover_sla_flag,
  itapprover_actual_start_datetime,
  itapprover_completed_datetime,
  itapprover_duration,
  itapprover_available_duration,
  itapprover_actual_duration,
  redmine_implementation_issue AS implementation_issue,
  GREATEST((SELECT itapprover_actual_duration - itapprover_available_duration), 0) AS exceed_duration
FROM
  sla_byapprover
WHERE YEAR (itapprover_completed_datetime) = ? AND MONTH (itapprover_completed_datetime) = ?
  AND deleted = 0
  AND (redmine_child_parent NOT IN ('CHILD') OR redmine_child_parent IS NULL)
  -- AND YEAR (itapprover_case_created) = ? AND MONTH (itapprover_case_created) = ?
  -- AND YEAR (itseverity_insert_data_datetime) = ? AND MONTH (itseverity_insert_data_datetime) = ?
  ORDER BY case_number ASC;;
