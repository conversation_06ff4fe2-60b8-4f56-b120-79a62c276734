#!/usr/bin/env python3
"""
Test validation of generated reports
"""

from validation_testing import ReportValidator
import logging

logging.basicConfig(level=logging.INFO)

def main():
    validator = ReportValidator()
    
    # Test the reports we've generated
    reports_to_test = [
        'Customer Service Performance (Telephone)',
        'System Availability (Infra)', 
        'System Performance (Response Time)'
    ]
    
    for report_name in reports_to_test:
        print(f'\n=== Validating {report_name} ===')
        result = validator.validate_single_report(report_name)
        print(f'Validation successful: {result["validation_successful"]}')
        if not result['validation_successful']:
            print(f'Error: {result["error_message"]}')
            if result['comparison_result']:
                comp = result['comparison_result']
                print(f'Differences: {len(comp["differences"])}')
                for diff in comp['differences'][:3]:
                    print(f'  - {diff}')
        else:
            print('✓ Report matches example file perfectly!')

if __name__ == "__main__":
    main()
