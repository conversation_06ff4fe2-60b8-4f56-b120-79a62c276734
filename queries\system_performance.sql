-- System Performance (Response Time)
-- Generated from: knowage\System Performance (Response Time).rptdesign
-- Parameters: year, month
-- Database: cdc_poms

SELECT '1. Login' AS trans_name, IFNULL(SUM(duration_result < 3000), 0) AS within_sla, IFNULL(SUM(duration_result > 3000), 0) AS exceed_sla, IFNULL(SUM(duration_result < 3000) + SUM(duration_result > 3000), 0) AS total_trans, IFNULL((SUM(duration_result > 3000)) / (SUM(duration_result < 3000) + SUM(duration_result > 3000)) * 100, 0) AS exceed_percentage, IFNULL(100 - (SUM(duration_result > 3000)) / (SUM(duration_result < 3000) + SUM(duration_result > 3000)) * 100, 0) AS within_percentage 
FROM performance_proxies 
WHERE MONTH(date_time) = ? 
AND YEAR(date_time) = ? 
AND TRANSACTION = 'my.ep.web.portal.login.LoginPortlet.executedTime' 
AND MODULE = 'SLA-PORTAL-LOGIN' 
UNION 
SELECT '2. Procurement Plan Submission' AS trans_name, IFNULL(SUM(duration_result < 3000), 0) AS within_sla, IFNULL(SUM(duration_result > 3000), 0) AS exceed_sla, IFNULL(SUM(duration_result < 3000) + SUM(duration_result > 3000), 0) AS total_trans, IFNULL((SUM(duration_result > 3000)) / (SUM(duration_result < 3000) + SUM(duration_result > 3000)) * 100, 0) AS exceed_percentage, IFNULL(100 - (SUM(duration_result > 3000)) / (SUM(duration_result < 3000) + SUM(duration_result > 3000)) * 100, 0) AS within_percentage 
FROM performance_proxies 
WHERE MONTH(date_time) = ? 
AND YEAR(date_time) = ? 
AND TRANSACTION = 'ProcurementPlanBackingBean.savePlan.executedTime' 
AND MODULE = 'SLA-PM-PS' 
UNION 
SELECT '3. Proposal Submission' AS trans_name, IFNULL(SUM(duration_result < 3000), 0) AS within_sla, IFNULL(SUM(duration_result > 3000), 0) AS exceed_sla, IFNULL(SUM(duration_result < 3000) + SUM(duration_result > 3000), 0) AS total_trans, IFNULL((SUM(duration_result > 3000)) / (SUM(duration_result < 3000) + SUM(duration_result > 3000)) * 100, 0) AS exceed_percentage, IFNULL(100 - (SUM(duration_result > 3000)) / (SUM(duration_result < 3000) + SUM(duration_result > 3000)) * 100, 0) AS within_percentage 
FROM performance_proxies 
WHERE MONTH(date_time) = ? 
AND YEAR(date_time) = ? 
AND TRANSACTION = 'SPDetailBackingBean.submitSupplierProposal.executedTime' 
AND MODULE = 'SLA-SC-PN' 
UNION 
SELECT '4. Request Note Submission' AS trans_name, IFNULL(SUM(duration_result < 3000), 0) AS within_sla, IFNULL(SUM(duration_result > 3000), 0) AS exceed_sla, IFNULL(SUM(duration_result < 3000) + SUM(duration_result > 3000), 0) AS total_trans, IFNULL((SUM(duration_result > 3000)) / (SUM(duration_result < 3000) + SUM(duration_result > 3000)) * 100, 0) AS exceed_percentage, IFNULL(100 - (SUM(duration_result > 3000)) / (SUM(duration_result < 3000) + SUM(duration_result > 3000)) * 100, 0) AS within_percentage 
FROM performance_proxies 
WHERE MONTH(date_time) = ? 
AND YEAR(date_time) = ? 
AND TRANSACTION = 'RequestNoteSO.submitRNForApproval.executedTime' 
UNION 
SELECT '5. Online Bidding' AS trans_name, IFNULL(SUM(duration_result < 3000), 0) AS within_sla, IFNULL(SUM(duration_result > 3000), 0) AS exceed_sla, IFNULL(SUM(duration_result < 3000) + SUM(duration_result > 3000), 0) AS total_trans, IFNULL((SUM(duration_result > 3000)) / (SUM(duration_result < 3000) + SUM(duration_result > 3000)) * 100, 0) AS exceed_percentage, IFNULL(100 - (SUM(duration_result > 3000)) / (SUM(duration_result < 3000) + SUM(duration_result > 3000)) * 100, 0) AS within_percentage 
FROM performance_proxies 
WHERE MONTH(date_time) = 11 
AND YEAR(date_time) = 2020 
AND TRANSACTION = 'OnlineBiddingBackingBean.updateBidPriceAndCalculateRank.executedTime' 
AND MODULE = 'SLA-SC-BD' 
UNION 
SELECT '6. Fulfilment Details (FD) Submission' AS trans_name, IFNULL(SUM(duration_result < 3000), 0) AS within_sla, IFNULL(SUM(duration_result > 3000), 0) AS exceed_sla, IFNULL(SUM(duration_result < 3000) + SUM(duration_result > 3000), 0) AS total_trans, IFNULL((SUM(duration_result > 3000)) / (SUM(duration_result < 3000) + SUM(duration_result > 3000)) * 100, 0) AS exceed_percentage, IFNULL(100 - (SUM(duration_result > 3000)) / (SUM(duration_result < 3000) + SUM(duration_result > 3000)) * 100, 0) AS within_percentage 
FROM performance_proxies 
WHERE MONTH(date_time) = ? 
AND YEAR(date_time) = ? 
AND TRANSACTION = 'ContractSO.saveContractVer.executedTime' 
UNION 
SELECT '7. To View The Final Agreement' AS trans_name, IFNULL(SUM(duration_result < 3000), 0) AS within_sla, IFNULL(SUM(duration_result > 3000), 0) AS exceed_sla, IFNULL(SUM(duration_result < 3000) + SUM(duration_result > 3000), 0) AS total_trans, IFNULL((SUM(duration_result > 3000)) / (SUM(duration_result < 3000) + SUM(duration_result > 3000)) * 100, 0) AS exceed_percentage, IFNULL(100 - (SUM(duration_result > 3000)) / (SUM(duration_result < 3000) + SUM(duration_result > 3000)) * 100, 0) AS within_percentage 
FROM performance_proxies 
WHERE MONTH(date_time) = ? 
AND YEAR(date_time) = ? 
AND TRANSACTION = 'AgreementSO.saveAgreement.executedTime' 
AND MODULE IN ('SLA-CT-AC','SLA-CT-SA', 'SLA-CT-FD') 
UNION 
SELECT '8. Purchase Request (PR) Submission' AS trans_name, IFNULL(SUM(duration_result < 3000), 0) AS within_sla, IFNULL(SUM(duration_result > 3000), 0) AS exceed_sla, IFNULL(SUM(duration_result < 3000) + SUM(duration_result > 3000), 0) AS total_trans, IFNULL((SUM(duration_result > 3000)) / (SUM(duration_result < 3000) + SUM(duration_result > 3000)) * 100, 0) AS exceed_percentage, IFNULL(100 - (SUM(duration_result > 3000)) / (SUM(duration_result < 3000) + SUM(duration_result > 3000)) * 100, 0) AS within_percentage 
FROM performance_proxies 
WHERE MONTH(date_time) = ? 
AND YEAR(date_time) = ? 
AND TRANSACTION = 'ReceivedNoteSO.saveReceivedNote.executedTime' 
UNION 
SELECT '9. Fulfilment Received Notes (FRN) Creation' AS trans_name, IFNULL(SUM(duration_result < 3000), 0) AS within_sla, IFNULL(SUM(duration_result > 3000), 0) AS exceed_sla, IFNULL(SUM(duration_result < 3000) + SUM(duration_result > 3000), 0) AS total_trans, IFNULL((SUM(duration_result > 3000)) / (SUM(duration_result < 3000) + SUM(duration_result > 3000)) * 100, 0) AS exceed_percentage, IFNULL(100 - (SUM(duration_result > 3000)) / (SUM(duration_result < 3000) + SUM(duration_result > 3000)) * 100, 0) AS within_percentage 
FROM performance_proxies 
WHERE MONTH(date_time) = ? 
AND YEAR(date_time) = ? 
AND TRANSACTION = 'FulfilmentRequestSO.saveFulfillmentRequest.executedTime' 
UNION 
SELECT '10. Invoice Creation' AS trans_name, IFNULL(SUM(duration_result < 3000), 0) AS within_sla, IFNULL(SUM(duration_result > 3000), 0) AS exceed_sla, IFNULL(SUM(duration_result < 3000) + SUM(duration_result > 3000), 0) AS total_trans, IFNULL((SUM(duration_result > 3000)) / (SUM(duration_result < 3000) + SUM(duration_result > 3000)) * 100, 0) AS exceed_percentage, IFNULL(100 - (SUM(duration_result > 3000)) / (SUM(duration_result < 3000) + SUM(duration_result > 3000)) * 100, 0) AS within_percentage 
FROM performance_proxies 
WHERE MONTH(date_time) = ? 
AND YEAR(date_time) = ? 
AND TRANSACTION = 'InvoiceSO.saveInvoice.executedTime' 
UNION 
SELECT '11. Submission Of MOF Account Application' AS trans_name, IFNULL(SUM(duration_result < 3000), 0) AS within_sla, IFNULL(SUM(duration_result > 3000), 0) AS exceed_sla, IFNULL(SUM(duration_result < 3000) + SUM(duration_result > 3000), 0) AS total_trans, IFNULL((SUM(duration_result > 3000)) / (SUM(duration_result < 3000) + SUM(duration_result > 3000)) * 100, 0) AS exceed_percentage, IFNULL(100 - (SUM(duration_result > 3000)) / (SUM(duration_result < 3000) + SUM(duration_result > 3000)) * 100, 0) AS within_percentage 
FROM performance_proxies 
WHERE MONTH(date_time) = ? 
AND YEAR(date_time) = ? 
AND TRANSACTION = 'MofRegistrationSO.initiateSmApplicationTask.executedTime' 
UNION 
SELECT '12. Catalogue Search' AS trans_name, IFNULL(SUM(duration_result < 3000), 0) AS within_sla, IFNULL(SUM(duration_result > 3000), 0) AS exceed_sla, IFNULL(SUM(duration_result < 3000) + SUM(duration_result > 3000), 0) AS total_trans, IFNULL((SUM(duration_result > 3000)) / (SUM(duration_result < 3000) + SUM(duration_result > 3000)) * 100, 0) AS exceed_percentage, IFNULL(100 - (SUM(duration_result > 3000)) / (SUM(duration_result < 3000) + SUM(duration_result > 3000)) * 100, 0) AS within_percentage 
FROM performance_proxies 
WHERE MONTH(date_time) = ? 
AND YEAR(date_time) = ? 
AND TRANSACTION = 'CmTrackingDiaryBackingBean.search.executedTime' 
AND MODULE = 'SLA-CM-SC' 
UNION 
SELECT '13. Search for Template' AS trans_name, IFNULL(SUM(duration_result < 3000), 0) AS within_sla, IFNULL(SUM(duration_result > 3000), 0) AS exceed_sla, IFNULL(SUM(duration_result < 3000) + SUM(duration_result > 3000), 0) AS total_trans, IFNULL((SUM(duration_result > 3000)) / (SUM(duration_result < 3000) + SUM(duration_result > 3000)) * 100, 0) AS exceed_percentage, IFNULL(100 - (SUM(duration_result > 3000)) / (SUM(duration_result < 3000) + SUM(duration_result > 3000)) * 100, 0) AS within_percentage 
FROM performance_proxies 
WHERE MONTH(date_time) = ? 
AND YEAR(date_time) = ? 
AND TRANSACTION = 'SpecificationBackingBean.pageChangeListener.executedTime' 
AND MODULE = 'SLA-SC-SE' 
UNION 
SELECT '14. Detail Supplier Registration Application 
FROM Task' AS trans_name, IFNULL(SUM(duration_result < 3000), 0) AS within_sla, IFNULL(SUM(duration_result > 3000), 0) AS exceed_sla, IFNULL(SUM(duration_result < 3000) + SUM(duration_result > 3000), 0) AS total_trans, IFNULL((SUM(duration_result > 3000)) / (SUM(duration_result < 3000) + SUM(duration_result > 3000)) * 100, 0) AS exceed_percentage, IFNULL(100 - (SUM(duration_result > 3000)) / (SUM(duration_result < 3000) + SUM(duration_result > 3000)) * 100, 0) AS within_percentage 
FROM performance_proxies 
WHERE MONTH(date_time) = ? 
AND YEAR(date_time) = ? 
AND TRANSACTION = 'VirtualCertBackingBean.viewVirtualCert.executedTime' 
UNION 
SELECT '15. Load Virtual Certificate' AS trans_name, IFNULL(SUM(duration_result < 3000), 0) AS within_sla, IFNULL(SUM(duration_result > 3000), 0) AS exceed_sla, IFNULL(SUM(duration_result < 3000) + SUM(duration_result > 3000), 0) AS total_trans, IFNULL((SUM(duration_result > 3000)) / (SUM(duration_result < 3000) + SUM(duration_result > 3000)) * 100, 0) AS exceed_percentage, IFNULL(100 - (SUM(duration_result > 3000)) / (SUM(duration_result < 3000) + SUM(duration_result > 3000)) * 100, 0) AS within_percentage 
FROM performance_proxies 
WHERE MONTH(date_time) = ? 
AND YEAR(date_time) = ? 
AND TRANSACTION = 'CommonApplBackingBean.preRenderView.executedTime' 
AND MODULE = 'SLA-SM-VA' 
UNION 
SELECT '16. View The Company Profile Report' AS trans_name, IFNULL(SUM(duration_result < 3000), 0) AS within_sla, IFNULL(SUM(duration_result > 3000), 0) AS exceed_sla, IFNULL(SUM(duration_result < 3000) + SUM(duration_result > 3000), 0) AS total_trans, IFNULL((SUM(duration_result > 3000)) / (SUM(duration_result < 3000) + SUM(duration_result > 3000)) * 100, 0) AS exceed_percentage, IFNULL(100 - (SUM(duration_result > 3000)) / (SUM(duration_result < 3000) + SUM(duration_result > 3000)) * 100, 0) AS within_percentage 
FROM performance_proxies 
WHERE MONTH(date_time) = ? 
AND YEAR(date_time) = ? 
AND TRANSACTION = 'ViewSuppProfileBackingBean.preRenderViewSupplierProfile.executedTime' 
AND MODULE IN ('SLA-SM-SP');
